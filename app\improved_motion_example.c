// improved_motion_example.c
//
// 改进运动控制器使用示例
// 演示如何使用improved_motion_controller进行路径规划和绘图

#include "improved_motion_example.h"
#include <stdio.h>
#include <string.h>

// 定义 M_PI 如果未定义
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 摄像头控制模式 - 删除本地路径生成
// 所有路径由摄像头端生成并发送

// 坐标转换参数
#define MOTOR_WORKSPACE_WIDTH  200   // 电机工作区宽度 (mm)
#define MOTOR_WORKSPACE_HEIGHT 200   // 电机工作区高度 (mm)
#define COORD_SCALE_FACTOR     1000  // 坐标缩放因子 (摄像头发送0-1000)

/**
 * @brief 初始化激光画图执行器 (摄像头主控模式)
 */
void improved_motion_example_init(void)
{
    // BSP初始化在main.c中已经完成
    // 这里可以添加其他初始化代码

    // 发送初始化完成消息
    uart_send("=== Laser Drawing Executor Initialized ===\r\n");
    uart_send("Mode: Camera-Controlled (Path Executor)\r\n");
    uart_send("Hardware: Using existing Emm_V5 motor system\r\n");
    uart_send("Communication: USART1 (115200 baud)\r\n");
    uart_send("Debug Output: USART1 (same as commands)\r\n");
    uart_send("Laser: Software control (bound to Y-axis motor)\r\n");
    uart_send("Waiting for camera commands...\r\n");
    uart_send("Type 'motion_help' to see available commands\r\n");
    uart_send("===========================================\r\n");
}

// ========== 摄像头协议处理函数 ==========

/**
 * @brief 坐标转换：标准化坐标转换为电机坐标
 * @param nx 标准化X坐标 (0-1000)
 * @param ny 标准化Y坐标 (0-1000)
 * @return 电机坐标点
 */
Point normalize_to_motor_coord(int32_t nx, int32_t ny)
{
    Point motor_point;
    // 将标准化坐标(0-1000)转换为电机坐标
    motor_point.x = (nx * MOTOR_WORKSPACE_WIDTH) / COORD_SCALE_FACTOR;
    motor_point.y = (ny * MOTOR_WORKSPACE_HEIGHT) / COORD_SCALE_FACTOR;
    return motor_point;
}

/**
 * @brief 处理摄像头发送的校准数据
 * @param data 校准数据字符串 "x1,y1,x2,y2,x3,y3,x4,y4"
 */
void process_calibration_data(const char* data)
{
    uart_send("Received calibration data from camera\r\n");
    // TODO: 解析A4纸四角坐标，建立坐标变换关系
    // 这里可以存储校准参数，用于后续的坐标转换
    uart_send("Calibration completed\r\n");
}

/**
 * @brief 处理摄像头发送的路径数据
 * @param data 路径数据字符串 "count,x1,y1,x2,y2,..."
 */
void process_path_data(const char* data)
{
    uart_send("Receiving path data from camera...\r\n");

    // 清空之前的路径
    motion_emergency_stop();

    // 解析路径点数量
    int point_count = 0;
    sscanf(data, "%d", &point_count);

    if (point_count <= 0 || point_count > 256) {
        uart_send("Error: Invalid point count\r\n");
        return;
    }

    // 跳过点数量，找到坐标数据
    const char* coord_data = strchr(data, ',');
    if (coord_data == NULL) {
        uart_send("Error: Invalid path data format\r\n");
        return;
    }
    coord_data++; // 跳过逗号

    // 解析并添加路径点
    int32_t nx, ny;
    const char* ptr = coord_data;
    for (int i = 0; i < point_count; i++) {
        if (sscanf(ptr, "%ld,%ld", &nx, &ny) == 2) {
            Point motor_point = normalize_to_motor_coord(nx, ny);
            motion_add_path_point(motor_point);

            // 移动到下一个坐标对
            ptr = strchr(ptr, ',');
            if (ptr) ptr = strchr(ptr + 1, ',');
            if (ptr) ptr++;
        } else {
            break;
        }
    }

    char msg[64];
    snprintf(msg, sizeof(msg), "Path loaded: %d points\r\n", point_count);
    uart_send(msg);
}

/**
 * @brief 处理摄像头发送的控制命令
 * @param command 控制命令字符串 "START/STOP/PAUSE/RESUME"
 */
void process_control_command(const char* command)
{
    if (strncmp(command, "START", 5) == 0) {
        motion_start_drawing();
        uart_send("Drawing started by camera command\r\n");
    }
    else if (strncmp(command, "STOP", 4) == 0) {
        motion_emergency_stop();
        uart_send("Drawing stopped by camera command\r\n");
    }
    else if (strncmp(command, "PAUSE", 5) == 0) {
        // TODO: 实现暂停功能
        uart_send("Drawing paused (not implemented)\r\n");
    }
    else if (strncmp(command, "RESUME", 6) == 0) {
        // TODO: 实现恢复功能
        uart_send("Drawing resumed (not implemented)\r\n");
    }
    else {
        uart_send("Unknown control command\r\n");
    }
}

/**
 * @brief 处理摄像头发送的跟踪数据
 * @param data 跟踪数据字符串 "x,y"
 */
void process_tracking_data(const char* data)
{
    int32_t nx, ny;
    if (sscanf(data, "%ld,%ld", &nx, &ny) == 2) {
        Point motor_point = normalize_to_motor_coord(nx, ny);

        // 发送位置反馈给摄像头
        char msg[64];
        snprintf(msg, sizeof(msg), "TRACK_ACK,%ld,%ld\r\n", motor_point.x, motor_point.y);
        uart_send(msg);

        // TODO: 实现闭环位置控制
    }
}

/**
 * @brief 发送系统状态给摄像头
 */
void send_status_to_camera(void)
{
    SystemState state = motion_get_system_state();
    Point position = motion_get_current_position();

    char status_msg[128];
    snprintf(status_msg, sizeof(status_msg),
             "STATUS,%d,%ld,%ld\r\n",
             state, position.x, position.y);
    uart_send(status_msg);
}

/**
 * @brief 本地紧急停止 (保留用于安全)
 */
void local_emergency_stop(void)
{
    motion_emergency_stop();
    uart_send("Local emergency stop executed!\r\n");
}

/**
 * @brief 处理摄像头和本地命令 (摄像头主控模式)
 * @param cmd 接收到的命令字符串
 */
void process_motion_command(const char* cmd)
{
    // ========== 摄像头协议命令 ==========
    if (strncmp(cmd, "CAL,", 4) == 0) {
        // 处理摄像头发送的校准数据
        process_calibration_data(cmd + 4);
    }
    else if (strncmp(cmd, "PATH,", 5) == 0) {
        // 处理摄像头发送的路径数据
        process_path_data(cmd + 5);
    }
    else if (strncmp(cmd, "CMD,", 4) == 0) {
        // 处理摄像头发送的控制命令
        process_control_command(cmd + 4);
    }
    else if (strncmp(cmd, "TRACK,", 6) == 0) {
        // 处理摄像头发送的跟踪数据
        process_tracking_data(cmd + 6);
    }

    // ========== 本地调试命令 (保留用于测试) ==========
    else if (strncmp(cmd, "debug_status", 12) == 0) {
        send_status_to_camera();
    }
    else if (strncmp(cmd, "debug_stop", 10) == 0) {
        local_emergency_stop();
    }
    else if (strncmp(cmd, "debug_profile", 13) == 0) {
        // 切换运动曲线类型
        static MotionProfileType current_profile = PROFILE_S_CURVE;
        if (current_profile == PROFILE_S_CURVE) {
            current_profile = PROFILE_TRAPEZOIDAL;
            motion_set_profile_type(PROFILE_TRAPEZOIDAL);
            uart_send("Debug: Switched to Trapezoidal profile\r\n");
        } else {
            current_profile = PROFILE_S_CURVE;
            motion_set_profile_type(PROFILE_S_CURVE);
            uart_send("Debug: Switched to S-Curve profile\r\n");
        }
    }
    else if (strncmp(cmd, "help", 4) == 0) {
        uart_send("=== Laser Drawing Executor (Camera-Controlled) ===\r\n");
        uart_send("Camera Protocol Commands:\r\n");
        uart_send("  CAL,x1,y1,x2,y2,x3,y3,x4,y4  - Calibration data\r\n");
        uart_send("  PATH,count,x1,y1,x2,y2,...   - Path data\r\n");
        uart_send("  CMD,START/STOP/PAUSE/RESUME  - Control commands\r\n");
        uart_send("  TRACK,x,y                    - Tracking data\r\n");
        uart_send("\r\nLocal Debug Commands:\r\n");
        uart_send("  debug_status   - Get system status\r\n");
        uart_send("  debug_stop     - Emergency stop\r\n");
        uart_send("  debug_profile  - Switch motion profile\r\n");
        uart_send("  help           - Show this help\r\n");
        uart_send("\r\nNote: System is in Camera-Controlled mode\r\n");
        uart_send("      All path planning done by camera\r\n");
        uart_send("      STM32 acts as path executor only\r\n");
        uart_send("================================================\r\n");
    }
    else {
        uart_send("Unknown command. Type 'help' for available commands.\r\n");
    }
}

/**
 * @brief 激光画图执行器主循环处理 (摄像头主控模式)
 * 这个函数应该在主循环中定期调用
 */
void improved_motion_example_process(void)
{
    static uint32_t last_status_time = 0;
    static uint32_t last_heartbeat_time = 0;
    uint32_t current_time = get_tick();

    // 每2秒向摄像头发送状态信息
    if (current_time - last_status_time > 2000) {
        send_status_to_camera();
        last_status_time = current_time;
    }

    // 每10秒发送心跳信号
    if (current_time - last_heartbeat_time > 10000) {
        uart_send("HEARTBEAT\r\n");
        last_heartbeat_time = current_time;
    }

    // 检查绘图完成状态
    static SystemState last_state = SYSTEM_IDLE;
    SystemState current_state = motion_get_system_state();

    if (last_state == SYSTEM_DRAWING && current_state == SYSTEM_IDLE) {
        // 绘图完成，通知摄像头
        uart_send("DRAWING_COMPLETE\r\n");
    }

    last_state = current_state;
}
