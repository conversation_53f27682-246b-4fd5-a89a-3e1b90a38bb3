// improved_motion_example.c
//
// 改进运动控制器使用示例
// 演示如何使用improved_motion_controller进行路径规划和绘图

#include "improved_motion_example.h"

// 定义 M_PI 如果未定义
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 示例路径点数据
static Point example_path[] = {
    {0, 0},       // 起点
    {100, 0},     // 向右移动
    {100, 100},   // 向上移动
    {0, 100},     // 向左移动
    {0, 0}        // 回到起点
};

static Point circle_path[36];  // 圆形路径点缓冲区

/**
 * @brief 初始化改进运动控制器示例
 */
void improved_motion_example_init(void)
{
    // BSP初始化在main.c中已经完成
    // 这里可以添加其他初始化代码

    // 发送初始化完成消息和使用说明
    uart_send("=== Improved Motion Controller Example Initialized ===\r\n");
    uart_send("Hardware: Using existing Emm_V5 motor system\r\n");
    uart_send("Communication: USART6 (changed from UART5)\r\n");
    uart_send("Laser: Software control (bound to Y-axis motor)\r\n");
    uart_send("Type 'motion_help' for available commands\r\n");
    uart_send("======================================================\r\n");
}

/**
 * @brief 绘制矩形路径示例
 */
void draw_rectangle_example(void)
{
    uart_send("Starting rectangle drawing...\r\n");
    
    // 清空之前的路径
    motion_emergency_stop();
    
    // 添加矩形路径点
    for (int i = 0; i < sizeof(example_path) / sizeof(Point); i++) {
        motion_add_path_point(example_path[i]);
    }
    
    // 开始绘图
    motion_start_drawing();
}

/**
 * @brief 生成圆形路径点
 * @param center_x 圆心X坐标
 * @param center_y 圆心Y坐标
 * @param radius 半径
 */
void generate_circle_path(int32_t center_x, int32_t center_y, int32_t radius)
{
    const int num_points = 36;  // 36个点，每10度一个点
    
    for (int i = 0; i < num_points; i++) {
        float angle = (float)i * 2.0f * M_PI / num_points;
        circle_path[i].x = center_x + (int32_t)(radius * cosf(angle));
        circle_path[i].y = center_y + (int32_t)(radius * sinf(angle));
    }
}

/**
 * @brief 绘制圆形路径示例
 */
void draw_circle_example(void)
{
    uart_send("Starting circle drawing...\r\n");
    
    // 清空之前的路径
    motion_emergency_stop();
    
    // 生成圆形路径 (中心在(50,50)，半径50)
    generate_circle_path(50, 50, 50);
    
    // 添加圆形路径点
    for (int i = 0; i < 36; i++) {
        motion_add_path_point(circle_path[i]);
    }
    
    // 开始绘图
    motion_start_drawing();
}

/**
 * @brief 切换运动曲线类型示例
 */
void switch_profile_example(void)
{
    static MotionProfileType current_profile = PROFILE_S_CURVE;
    
    // 切换曲线类型
    if (current_profile == PROFILE_S_CURVE) {
        current_profile = PROFILE_TRAPEZOIDAL;
        motion_set_profile_type(PROFILE_TRAPEZOIDAL);
        uart_send("Switched to Trapezoidal profile\r\n");
    } else {
        current_profile = PROFILE_S_CURVE;
        motion_set_profile_type(PROFILE_S_CURVE);
        uart_send("Switched to S-Curve profile\r\n");
    }
}

/**
 * @brief 获取系统状态示例
 */
void get_status_example(void)
{
    SystemState state = motion_get_system_state();
    Point position = motion_get_current_position();
    
    char status_msg[128];
    snprintf(status_msg, sizeof(status_msg), 
             "System State: %d, Position: (%ld, %ld)\r\n", 
             state, position.x, position.y);
    uart_send(status_msg);
}

/**
 * @brief 紧急停止示例
 */
void emergency_stop_example(void)
{
    motion_emergency_stop();
    uart_send("Emergency stop executed!\r\n");
}

/**
 * @brief 处理串口命令
 * @param cmd 接收到的命令字符串
 */
void process_motion_command(const char* cmd)
{
    if (strncmp(cmd, "rect", 4) == 0) {
        draw_rectangle_example();
    }
    else if (strncmp(cmd, "circle", 6) == 0) {
        draw_circle_example();
    }
    else if (strncmp(cmd, "switch", 6) == 0) {
        switch_profile_example();
    }
    else if (strncmp(cmd, "status", 6) == 0) {
        get_status_example();
    }
    else if (strncmp(cmd, "stop", 4) == 0) {
        emergency_stop_example();
    }
    else if (strncmp(cmd, "help", 4) == 0) {
        uart_send("=== Improved Motion Controller Commands ===\r\n");
        uart_send("  rect   - Draw rectangle path\r\n");
        uart_send("  circle - Draw circle path\r\n");
        uart_send("  switch - Switch motion profile (Trapezoidal/S-Curve)\r\n");
        uart_send("  status - Get system status and position\r\n");
        uart_send("  stop   - Emergency stop all motion\r\n");
        uart_send("  help   - Show this help\r\n");
        uart_send("\r\nNote: Uses existing Emm_V5 motor system\r\n");
        uart_send("      Laser control is software-based\r\n");
        uart_send("      Communication via USART6\r\n");
        uart_send("==========================================\r\n");
    }
    else {
        uart_send("Unknown command. Type 'help' for available commands.\r\n");
    }
}

/**
 * @brief 改进运动控制器主循环处理
 * 这个函数应该在主循环中定期调用
 */
void improved_motion_example_process(void)
{
    // 这里可以添加周期性的状态检查或其他处理
    // 例如：检查运动是否完成，更新状态等
    
    static uint32_t last_status_time = 0;
    uint32_t current_time = get_tick();
    
    // 每5秒输出一次状态信息（可选）
    if (current_time - last_status_time > 5000) {
        SystemState state = motion_get_system_state();
        if (state == SYSTEM_DRAWING) {
            Point position = motion_get_current_position();
            char msg[64];
            snprintf(msg, sizeof(msg), "Drawing... Position: (%ld, %ld)\r\n", 
                     position.x, position.y);
            uart_send(msg);
        }
        last_status_time = current_time;
    }
}
