// improved_motion_example.c
//
// 改进运动控制器使用示例
// 演示如何使用improved_motion_controller进行路径规划和绘图

#include "improved_motion_example.h"
#include <stdio.h>
#include <string.h>

// 定义 M_PI 如果未定义
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 摄像头控制模式 - 删除本地路径生成
// 所有路径由摄像头端生成并发送

// 坐标转换参数
#define MOTOR_WORKSPACE_WIDTH  200   // 电机工作区宽度 (mm)
#define MOTOR_WORKSPACE_HEIGHT 200   // 电机工作区高度 (mm)
#define COORD_SCALE_FACTOR     1000  // 坐标缩放因子 (摄像头发送0-1000)

/**
 * @brief 初始化激光画图执行器 (摄像头数据直驱模式)
 */
void improved_motion_example_init(void)
{
    // BSP初始化在main.c中已经完成
    // 静默初始化，无调试输出
    // 系统进入数据驱动模式，等待摄像头数据
}

// ========== 摄像头协议处理函数 ==========

/**
 * @brief 坐标转换：标准化坐标转换为电机坐标
 * @param nx 标准化X坐标 (0-1000)
 * @param ny 标准化Y坐标 (0-1000)
 * @return 电机坐标点
 */
Point normalize_to_motor_coord(int32_t nx, int32_t ny)
{
    Point motor_point;
    // 将标准化坐标(0-1000)转换为电机坐标
    motor_point.x = (nx * MOTOR_WORKSPACE_WIDTH) / COORD_SCALE_FACTOR;
    motor_point.y = (ny * MOTOR_WORKSPACE_HEIGHT) / COORD_SCALE_FACTOR;
    return motor_point;
}

/**
 * @brief 处理摄像头发送的校准数据 (自动静默处理)
 * @param data 校准数据字符串 "x1,y1,x2,y2,x3,y3,x4,y4"
 */
void process_calibration_data(const char* data)
{
    // 静默解析A4纸四角坐标，建立坐标变换关系
    // TODO: 解析并存储校准参数，用于后续的坐标转换
    // 自动完成校准，无需反馈
}

/**
 * @brief 处理摄像头发送的路径数据 (自动启动绘图)
 * @param data 路径数据字符串 "count,x1,y1,x2,y2,..."
 */
void process_path_data(const char* data)
{
    // 静默清空之前的路径
    motion_emergency_stop();

    // 解析路径点数量
    int point_count = 0;
    sscanf(data, "%d", &point_count);

    if (point_count <= 0 || point_count > 256) {
        return; // 静默处理错误
    }

    // 跳过点数量，找到坐标数据
    const char* coord_data = strchr(data, ',');
    if (coord_data == NULL) {
        return; // 静默处理错误
    }
    coord_data++; // 跳过逗号

    // 解析并添加路径点
    int32_t nx, ny;
    const char* ptr = coord_data;
    for (int i = 0; i < point_count; i++) {
        if (sscanf(ptr, "%ld,%ld", &nx, &ny) == 2) {
            Point motor_point = normalize_to_motor_coord(nx, ny);
            motion_add_path_point(motor_point);

            // 移动到下一个坐标对
            ptr = strchr(ptr, ',');
            if (ptr) ptr = strchr(ptr + 1, ',');
            if (ptr) ptr++;
        } else {
            break;
        }
    }

    // 自动启动绘图，无需等待命令
    motion_start_drawing();
}

// 删除控制命令处理 - 改为数据直驱模式

/**
 * @brief 处理摄像头发送的跟踪数据 (静默位置修正)
 * @param data 跟踪数据字符串 "x,y"
 */
void process_tracking_data(const char* data)
{
    int32_t nx, ny;
    if (sscanf(data, "%ld,%ld", &nx, &ny) == 2) {
        Point motor_point = normalize_to_motor_coord(nx, ny);
        // TODO: 实现静默的闭环位置控制
        // 根据激光实际位置与目标位置的差异进行修正
    }
}

// 删除状态反馈和本地控制函数 - 改为纯数据驱动模式

/**
 * @brief 简化的数据处理函数 (摄像头数据直驱模式)
 * @param data 接收到的数据字符串
 */
void process_camera_data(const char* data)
{
    // 简化的数据协议处理，无调试输出
    if (strncmp(data, "CAL,", 4) == 0) {
        // 处理摄像头发送的校准数据
        process_calibration_data(data + 4);
    }
    else if (strncmp(data, "PATH,", 5) == 0) {
        // 处理摄像头发送的路径数据并自动启动绘图
        process_path_data(data + 5);
    }
    else if (strncmp(data, "TRACK,", 6) == 0) {
        // 处理摄像头发送的跟踪数据
        process_tracking_data(data + 6);
    }
    // 静默忽略未知数据
}

/**
 * @brief 激光画图执行器主循环处理 (数据直驱模式)
 * 这个函数应该在主循环中定期调用
 */
void improved_motion_example_process(void)
{
    // 静默运行，无状态输出
    // 系统完全由摄像头数据驱动
    // 只专注于运动控制执行
}
