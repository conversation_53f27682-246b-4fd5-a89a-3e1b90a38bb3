#ifndef __BASIC_H__
#define __BASIC_H__

#include "bsp_system.h"
#include "pi_bsp.h"

// 控制模式枚举
typedef enum {
    BASIC_MODE_TRACKING = 0,    // 追踪模式（原有功能）
    BASIC_MODE_RESET,           // 复位模式
    BASIC_MODE_SCREEN_EDGE,     // 屏幕边线模式
    BASIC_MODE_A4_TAPE,         // A4胶带模式
    BASIC_MODE_ROTATED_A4       // 旋转A4模式
} BasicControlMode_t;

// 复位状态枚举
typedef enum {
    RESET_STATUS_IDLE = 0,      // 空闲状态
    RESET_STATUS_MOVING,        // 正在移动到原点
    RESET_STATUS_COMPLETED,     // 复位完成
    RESET_STATUS_TIMEOUT        // 复位超时
} ResetStatus_t;

// 屏幕参数定义（假设常见分辨率）
#define SCREEN_WIDTH        640     // 屏幕宽度（像素）
#define SCREEN_HEIGHT       480     // 屏幕高度（像素）
#define SCREEN_CENTER_X     320     // 屏幕中心X坐标（原点）
#define SCREEN_CENTER_Y     240     // 屏幕中心Y坐标（原点）

// 误差阈值定义（2cm对应的像素值，假设1cm约等于10像素）
#define RESET_ERROR_THRESHOLD   20  // 2cm误差对应20像素
#define RESET_TIMEOUT_MS        10000  // 复位超时时间10秒

// 全局变量声明
extern BasicControlMode_t g_basic_control_mode;
extern int g_target_red_x, g_target_red_y;

// 函数声明

/**
 * @brief 初始化basic模块
 */
void basic_init(void);

/**
 * @brief 设置控制模式
 * @param mode 控制模式
 */
void basic_set_mode(BasicControlMode_t mode);

/**
 * @brief 获取当前控制模式
 * @return 当前控制模式
 */
BasicControlMode_t basic_get_mode(void);

/**
 * @brief 启动复位功能
 * @return 0:成功启动, -1:失败
 */
int basic_start_reset(void);

/**
 * @brief 获取复位状态
 * @return 复位状态
 */
ResetStatus_t basic_get_reset_status(void);

/**
 * @brief 检查复位是否完成
 * @return 1:完成, 0:未完成
 */
int basic_is_reset_completed(void);

/**
 * @brief basic模块处理函数（需要在主循环中调用）
 */
void basic_proc(void);

/**
 * @brief 更新原点位置（当检测到新的矩形时调用）
 */
void basic_update_origin(void);

/**
 * @brief 计算两点之间的距离
 * @param x1, y1 第一个点的坐标
 * @param x2, y2 第二个点的坐标
 * @return 距离（像素）
 */
float basic_calculate_distance(int x1, int y1, int x2, int y2);

#endif /* __BASIC_H__ */
