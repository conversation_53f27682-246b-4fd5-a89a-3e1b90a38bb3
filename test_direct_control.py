#!/usr/bin/env python3
# test_direct_control.py
# 
# 测试修复后的数据直驱控制

from micu_uart_lib import SimpleUART, micu_printf
import time

def test_calibration():
    """测试校准数据发送"""
    print("发送校准数据...")
    # 发送A4纸四角坐标 (模拟160x120分辨率)
    micu_printf("CAL,0,0,159,0,159,119,0,119")
    time.sleep(1)

def test_simple_path():
    """测试简单路径数据发送"""
    print("发送简单路径数据...")
    # 发送一个简单的三角形路径 (3个点)
    micu_printf("PATH,3,500,200,700,600,300,600")
    time.sleep(2)

def test_tracking():
    """测试跟踪数据发送"""
    print("发送跟踪数据...")
    # 发送激光位置数据
    micu_printf("TRACK,500,400")
    time.sleep(1)

def main():
    print("=== 数据直驱控制测试程序 ===")
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
    else:
        print("串口初始化失败")
        return
    
    # 设置帧格式
    uart.set_frame("$$", "##", True)
    print("帧格式设置完成")
    
    try:
        while True:
            print("\n选择测试项目:")
            print("1. 测试校准数据")
            print("2. 测试路径数据")
            print("3. 测试跟踪数据")
            print("4. 连续测试")
            print("5. 退出")
            
            choice = input("请输入选择 (1-5): ").strip()
            
            if choice == '1':
                test_calibration()
            elif choice == '2':
                test_simple_path()
            elif choice == '3':
                test_tracking()
            elif choice == '4':
                print("开始连续测试...")
                test_calibration()
                time.sleep(2)
                test_simple_path()
                time.sleep(3)
                test_tracking()
                time.sleep(1)
                print("连续测试完成")
            elif choice == '5':
                break
            else:
                print("无效选择，请重新输入")
                
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
    
    print("测试程序结束")

if __name__ == "__main__":
    main()
