#ifndef __IMPROVED_MOTION_BSP_H__
#define __IMPROVED_MOTION_BSP_H__

#include "bsp_system.h"
#include "improved_motion_controller.h"

/* 硬件配置宏定义 */
#define MOTION_TIMER_INSTANCE       TIM1                    // 步进电机定时器

// 串口配置选项 - 用户可以根据需要选择
// 选项1: 使用UART5 (原配置)
// #define MOTION_UART_INSTANCE        huart5                  // 通信串口 (UART5)

// 选项2: 使用USART6 (新配置，根据用户要求)
#define MOTION_UART_INSTANCE        huart6                  // 通信串口 (USART6)

#define MOTION_LASER_PWM_TIMER      TIM3                    // 激光PWM定时器
#define MOTION_LASER_PWM_CHANNEL    TIM_CHANNEL_1           // 激光PWM通道

/* GPIO引脚定义 */
#define X_MOTOR_STEP_PORT           GPIOB
#define X_MOTOR_STEP_PIN            GPIO_PIN_0
#define X_MOTOR_DIR_PORT            GPIOB  
#define X_MOTOR_DIR_PIN             GPIO_PIN_1

#define Y_MOTOR_STEP_PORT           GPIOB
#define Y_MOTOR_STEP_PIN            GPIO_PIN_2
#define Y_MOTOR_DIR_PORT            GPIOB
#define Y_MOTOR_DIR_PIN             GPIO_PIN_3

#define LASER_ENABLE_PORT           GPIOB
#define LASER_ENABLE_PIN            GPIO_PIN_4

/* 运动控制参数 */
#define MOTION_TIMER_FREQ           72000000UL              // 定时器频率 (72MHz)
#define MOTION_MIN_PERIOD           100                     // 最小定时器周期
#define MOTION_MAX_PERIOD           0xFFFFFFFF              // 最大定时器周期
#define LASER_MAX_INTENSITY         1000                    // 激光最大强度

/* BSP接口函数声明 */
void improved_motion_bsp_init(void);
void improved_motion_gpio_init(void);
void improved_motion_timer_init(void);
void improved_motion_laser_init(void);

/* 硬件接口函数实现 - 这些函数被improved_motion_controller.c调用 */
void timer_set_period(uint32_t period);
void timer_enable(bool enable);
void step_x_motor(bool dir);
void step_y_motor(bool dir);
void laser_set_intensity(uint16_t intensity);
void uart_send(const char* msg);
uint32_t get_tick(void);

/* 中断处理函数 */
void motion_timer_irq_handler(void);

#endif // __IMPROVED_MOTION_BSP_H__
