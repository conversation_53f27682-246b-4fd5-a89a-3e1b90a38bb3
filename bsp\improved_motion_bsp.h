#ifndef __IMPROVED_MOTION_BSP_H__
#define __IMPROVED_MOTION_BSP_H__

#include "bsp_system.h"

/* 硬件配置宏定义 */
#define MOTION_TIMER_INSTANCE       TIM1                    // 运动控制定时器

// 串口配置 - 使用USART6 (根据用户要求)
#define MOTION_UART_INSTANCE        huart6                  // 通信串口 (USART6)

/* 电机配置 - 使用现有的电机控制接口 */
#define MOTION_X_MOTOR_UART         MOTOR_X_UART            // X轴电机串口 (huart2)
#define MOTION_Y_MOTOR_UART         MOTOR_Y_UART            // Y轴电机串口 (huart4)
#define MOTION_X_MOTOR_ADDR         MOTOR_X_ADDR            // X轴电机地址
#define MOTION_Y_MOTOR_ADDR         MOTOR_Y_ADDR            // Y轴电机地址
#define MOTION_MOTOR_SYNC_FLAG      MOTOR_SYNC_FLAG         // 电机同步标志

/* 运动控制参数 */
#define MOTION_TIMER_FREQ           72000000UL              // 定时器频率 (72MHz)
#define MOTION_UPDATE_FREQ          1000                    // 运动更新频率 (1kHz)
#define MOTION_MIN_PERIOD           (MOTION_TIMER_FREQ / 10000)  // 最小定时器周期 (10kHz)
#define MOTION_MAX_PERIOD           (MOTION_TIMER_FREQ / 10)     // 最大定时器周期 (10Hz)

/* 电机控制参数 */
#define MOTION_MAX_RPM              MOTOR_MAX_SPEED         // 最大转速 (RPM)
#define MOTION_ACCEL                MOTOR_ACCEL             // 加速度参数
#define MOTION_STEPS_PER_REV        200                     // 每转步数 (假设1.8度步进电机)

/* 激光控制参数 (软件实现，激光绑定在Y轴电机上) */
#define LASER_MAX_INTENSITY         100                     // 激光最大强度 (百分比)

/* BSP接口函数声明 */
void improved_motion_bsp_init(void);
void improved_motion_timer_init(void);
void improved_motion_motor_init(void);

/* 硬件接口函数实现 - 这些函数被improved_motion_controller.c调用 */
void timer_set_period(uint32_t period);
void timer_enable(bool enable);
void step_x_motor(bool dir);
void step_y_motor(bool dir);
void laser_set_intensity(uint16_t intensity);
void uart_send(const char* msg);
uint32_t get_tick(void);

/* 中断处理函数 */
void motion_timer_irq_handler(void);

/* 外部函数声明 - 来自 improved_motion_controller.c */
extern void motion_step_handler(void);

#endif // __IMPROVED_MOTION_BSP_H__
