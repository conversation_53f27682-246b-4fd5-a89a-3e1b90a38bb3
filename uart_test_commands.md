# 串口测试命令指南

## 🔧 **当前串口配置**

| 串口 | 用途 | 波特率 | 连接方式 |
|------|------|--------|----------|
| **USART1** | **调试输出 + 命令接收** | **115200** | **USB转串口** |
| USART2 | X轴电机通信 | - | 电机驱动器 |
| USART3 | HWT101陀螺仪 | - | 陀螺仪模块 |
| UART4 | Y轴电机通信 | - | 电机驱动器 |
| UART5 | 测试数据 | - | 备用 |
| USART6 | MaixCam数据 | 115200 | 摄像头模块 |

## 📡 **测试步骤**

### **1. 硬件连接检查**
```
STM32 USART1:
- TX (PA9)  → USB转串口 RX
- RX (PA10) → USB转串口 TX
- GND       → USB转串口 GND
```

### **2. 串口工具设置**
```
波特率: 115200
数据位: 8
停止位: 1
校验位: None
流控制: None
```

### **3. 复位后应该看到的信息**
```
=== Laser Drawing Executor Initialized ===
Mode: Camera-Controlled (Path Executor)
Hardware: Using existing Emm_V5 motor system
Communication: USART1 (115200 baud)
Debug Output: USART1 (same as commands)
Laser: Software control (bound to Y-axis motor)
Waiting for camera commands...
Type 'motion_help' to see available commands
===========================================
Improved Motion BSP initialized with existing motor system
Motors initialized using existing Emm_V5 system
Motion timer initialized for motor control updates
```

### **4. 测试命令**

#### **基本命令测试**
```bash
# 显示帮助信息
motion_help

# 获取系统状态
motion_debug_status

# 紧急停止测试
motion_debug_stop

# 切换运动曲线
motion_debug_profile
```

#### **摄像头协议测试**
```bash
# 校准数据测试
motion_CAL,10,10,150,10,150,110,10,110

# 路径数据测试 (3个点的三角形)
motion_PATH,3,500,500,600,400,400,400

# 控制命令测试
motion_CMD,START
motion_CMD,STOP
```

#### **跟踪数据测试**
```bash
# 发送跟踪位置
motion_TRACK,500,500
motion_TRACK,600,600
```

### **5. 预期响应**

#### **帮助命令响应**
```
=== Laser Drawing Executor (Camera-Controlled) ===
Camera Protocol Commands:
  CAL,x1,y1,x2,y2,x3,y3,x4,y4  - Calibration data
  PATH,count,x1,y1,x2,y2,...   - Path data
  CMD,START/STOP/PAUSE/RESUME  - Control commands
  TRACK,x,y                    - Tracking data

Local Debug Commands:
  debug_status   - Get system status
  debug_stop     - Emergency stop
  debug_profile  - Switch motion profile
  help           - Show this help

Note: System is in Camera-Controlled mode
      All path planning done by camera
      STM32 acts as path executor only
================================================
```

#### **状态命令响应**
```
STATUS,0,0,0
```

#### **路径命令响应**
```
Receiving path data from camera...
Path loaded: 3 points
```

#### **控制命令响应**
```
Drawing started by camera command
Drawing stopped by camera command
```

## 🚨 **故障排除**

### **问题1：复位后无任何输出**
**可能原因：**
- 串口连接错误
- 波特率设置错误
- STM32未正常启动

**解决方法：**
1. 检查硬件连接
2. 确认波特率为115200
3. 检查电源和复位电路

### **问题2：有输出但命令无响应**
**可能原因：**
- 命令格式错误
- 串口接收中断未启用
- 命令处理函数未调用

**解决方法：**
1. 确保命令以 `motion_` 开头
2. 检查串口中断配置
3. 验证 `process_motion_command` 函数

### **问题3：部分命令有响应，部分无响应**
**可能原因：**
- 特定命令处理逻辑错误
- 内存或缓冲区问题

**解决方法：**
1. 逐个测试命令
2. 检查命令解析逻辑
3. 验证内存使用情况

## 🔧 **调试技巧**

### **1. 添加调试输出**
在 `process_motion_command` 函数开头添加：
```c
uart_send("Received command: ");
uart_send(cmd);
uart_send("\r\n");
```

### **2. 检查串口状态**
```c
if (MOTION_UART_INSTANCE.gState != HAL_UART_STATE_READY) {
    uart_send("UART not ready!\r\n");
}
```

### **3. 监控系统状态**
定期输出系统状态，检查是否正常运行。

## 📋 **成功标志**

如果看到以下现象，说明系统工作正常：
1. ✅ 复位后立即有初始化信息输出
2. ✅ `motion_help` 命令有完整响应
3. ✅ `motion_debug_status` 显示系统状态
4. ✅ 摄像头协议命令能正确解析
5. ✅ 每2秒自动发送状态信息
6. ✅ 每10秒发送心跳信号

## 🎯 **下一步**

系统正常后，可以：
1. 连接摄像头测试完整通信
2. 测试实际的绘图功能
3. 验证电机控制响应
4. 优化通信协议和错误处理
