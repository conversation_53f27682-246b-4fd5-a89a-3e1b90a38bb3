#include "key_bsp.h"
#include "basic.h"
#include "uart_bsp.h"

uint8_t key_val = 0;
uint8_t key_old = 0;
uint8_t key_down = 0;
uint8_t key_up = 0;

// 复位按键相关变量
static uint32_t reset_key_press_time = 0;
static uint8_t reset_key_pressed = 0;

/**
 * @brief 按键模块初始化
 */
void key_init(void)
{
	key_val = 0;
	key_old = 0;
	key_down = 0;
	key_up = 0;
	reset_key_press_time = 0;
	reset_key_pressed = 0;

	my_printf(&huart1, "Key module initialized\r\n");
	my_printf(&huart1, "PE0 (KEY1) - Reset to rectangle center\r\n");
	my_printf(&huart1, "PE1 (KEY2) - Switch to tracking mode\r\n");
	my_printf(&huart1, "PE2 (KEY3) - Check status\r\n");
	my_printf(&huart1, "PE3 (KEY4) - Update origin\r\n");
}

uint8_t key_read(void)
{
	uint8_t temp = 0;

	// PE0 按键 (KEY1) - 用于复位功能
	if(HAL_GPIO_ReadPin(KEY1_GPIO_Port, KEY1_Pin) == GPIO_PIN_RESET)
		temp = 1;
	if(HAL_GPIO_ReadPin(KEY2_GPIO_Port,KEY2_Pin) == GPIO_PIN_RESET)
		temp = 2;
	if(HAL_GPIO_ReadPin(KEY3_GPIO_Port,KEY3_Pin) == GPIO_PIN_RESET)
		temp = 3;
	if(HAL_GPIO_ReadPin(KEY4_GPIO_Port,KEY4_Pin) == GPIO_PIN_RESET)
		temp = 4;
	return temp;
}

void key_proc(void)
{
	key_val = key_read();
	key_down = key_val & (key_val ^ key_old);
	key_up = ~key_val & (key_val ^ key_old);
	key_old = key_val;

	// PE0按键 (KEY1) - 触发复位功能
	if(key_down == 1)
	{
		reset_key_press_time = HAL_GetTick();
		reset_key_pressed = 1;
		my_printf(&huart1, "PE0 Key pressed - Starting reset to rectangle center\r\n");

		int result = basic_start_reset();
		if (result == 0) {
			my_printf(&huart1, "Reset started successfully\r\n");
		} else {
			my_printf(&huart1, "Reset failed: %d\r\n", result);
		}
	}

	// 按键释放时的处理
	if(key_up == 1 && reset_key_pressed)
	{
		uint32_t press_duration = HAL_GetTick() - reset_key_press_time;
		my_printf(&huart1, "PE0 Key released after %lu ms\r\n", press_duration);
		reset_key_pressed = 0;
	}

	// KEY2 (PE1) - 切换到追踪模式
	if(key_down == KEY_FUNC2)
	{
		my_printf(&huart1, "KEY2 pressed - Switching to tracking mode\r\n");
		basic_set_mode(BASIC_MODE_TRACKING);
	}

	// KEY3 (PE2) - 查询状态
	if(key_down == KEY_FUNC3)
	{
		my_printf(&huart1, "KEY3 pressed - Checking status\r\n");
		BasicControlMode_t mode = basic_get_mode();
		ResetStatus_t reset_status = basic_get_reset_status();

		my_printf(&huart1, "Mode: %d, Reset Status: %d\r\n", mode, reset_status);

		if (latest_red_laser_coord.isValid) {
			float distance = basic_calculate_distance(
				latest_red_laser_coord.x, latest_red_laser_coord.y,
				g_target_red_x, g_target_red_y);
			my_printf(&huart1, "Current: (%d,%d), Target: (%d,%d), Distance: %.1f px\r\n",
					  latest_red_laser_coord.x, latest_red_laser_coord.y,
					  g_target_red_x, g_target_red_y, distance);
		}
	}

	// KEY4 (PE3) - 更新原点位置
	if(key_down == KEY_FUNC4)
	{
		my_printf(&huart1, "KEY4 pressed - Updating origin\r\n");
		basic_update_origin();
	}
}
