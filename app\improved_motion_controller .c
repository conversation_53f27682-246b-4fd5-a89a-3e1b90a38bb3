// improved_motion_controller.c
//
// 该文件实现了改进的运动控制算法，支持梯形和S曲线加减速。
// 参考资料指出，S曲线通过在加速度和减速度之间添加过渡段能够
// 减少加速度的突变，降低系统振动【593956478117033†L110-L146】。因此本实现
// 在传统梯形控制基础上增加了S曲线控制逻辑。

#include "improved_motion_controller.h"
#include <stdlib.h>
#include <math.h>

// 注意：为了简化示例，本实现仍然使用浮点运算计算S曲线。
// 在实际的单片机上可根据需要使用查表或定点算法以降低开销。

// 路径点缓冲区
#define MAX_PATH_POINTS 256
static Point path_buffer[MAX_PATH_POINTS];
static uint16_t path_head = 0;
static uint16_t path_tail = 0;
static uint16_t path_count = 0;

// 电机配置
static MotorConfig x_motor_config = {0};
static MotorConfig y_motor_config = {0};

// 系统状态
static SystemState system_state = SYSTEM_IDLE;

// 当前坐标
static Point current_position = {0, 0};

// 目标坐标
static Point target_position = {0, 0};

// 运动状态
static MotionState motion_state = MOTION_IDLE;

// 运动曲线类型
static MotionProfileType profile_type = PROFILE_TRAPEZOIDAL;

// 运动控制变量
static int32_t total_steps = 0;
static int32_t steps_completed = 0;
static int32_t current_speed = 0;
static int32_t max_profile_speed = 0;
static int32_t acceleration_steps = 0;
static int32_t deceleration_steps = 0;
static int32_t cruise_steps = 0;

// Bresenham 算法变量
static int32_t dx, dy;
static int32_t sx, sy;
static int32_t err;
static int32_t e2;

// 步进定时器周期计算
static uint32_t calculate_step_interval() {
    if (current_speed <= 0) return 0xFFFFFFFF; // 最大值，不产生中断
    // 假设系统时钟为72MHz，则周期 = 72e6 / current_speed
    return (uint32_t)(72000000UL / current_speed);
}

// 外部依赖的硬件接口函数声明（由用户实现）
extern void timer_set_period(uint32_t period);
extern void timer_enable(bool enable);
extern void step_x_motor(bool dir);
extern void step_y_motor(bool dir);
extern void laser_set_intensity(uint16_t intensity);
extern void uart_send(const char* msg);
extern uint32_t get_tick();

// 初始化运动控制器
void improved_motion_controller_init(const MotorConfig* x_config,
                                     const MotorConfig* y_config,
                                     MotionProfileType type) {
    x_motor_config = *x_config;
    y_motor_config = *y_config;
    profile_type = type;
    system_state = SYSTEM_IDLE;
    motion_state = MOTION_IDLE;
    // 复位路径缓冲区
    path_head = 0;
    path_tail = 0;
    path_count = 0;
    current_position.x = 0;
    current_position.y = 0;
}

// 动态设置运动曲线类型
void motion_set_profile_type(MotionProfileType type) {
    profile_type = type;
}

// 添加路径点
void motion_add_path_point(Point point) {
    if (path_count < MAX_PATH_POINTS) {
        path_buffer[path_head] = point;
        path_head = (path_head + 1) % MAX_PATH_POINTS;
        path_count++;
        if (system_state == SYSTEM_IDLE) {
            system_state = SYSTEM_RECEIVING_PATH;
        }
    }
}

// 计算运动参数并准备下一段
static void prepare_motion_to_target() {
    // 计算 Bresenham 参数
    dx = abs(target_position.x - current_position.x);
    dy = abs(target_position.y - current_position.y);
    sx = (current_position.x < target_position.x) ? 1 : -1;
    sy = (current_position.y < target_position.y) ? 1 : -1;
    err = dx - dy;
    // 总步数取较大值
    total_steps = (dx > dy) ? dx : dy;
    steps_completed = 0;
    // 计算最大可用速度和加减速阶段步数
    int32_t max_speed = (x_motor_config.max_speed < y_motor_config.max_speed) ?
                        x_motor_config.max_speed : y_motor_config.max_speed;
    // 最小加速度/减速度
    int32_t accel = (x_motor_config.acceleration < y_motor_config.acceleration) ?
                    x_motor_config.acceleration : y_motor_config.acceleration;
    int32_t decel = (x_motor_config.deceleration < y_motor_config.deceleration) ?
                    x_motor_config.deceleration : y_motor_config.deceleration;
    // 最大速度可能受到加速度与总步数限制
    int32_t accel_max_speed = (int32_t)sqrtf(2.0f * accel * total_steps);
    if (accel_max_speed < max_speed) {
        max_speed = accel_max_speed;
    }
    max_profile_speed = max_speed;
    // 计算加速和减速阶段步数
    acceleration_steps = max_speed * max_speed / (2 * accel);
    deceleration_steps = max_speed * max_speed / (2 * decel);
    // 检查是否需要匀速阶段
    if (acceleration_steps + deceleration_steps > total_steps) {
        // 使用三角形速度曲线
        acceleration_steps = total_steps * decel / (accel + decel);
        deceleration_steps = total_steps - acceleration_steps;
        cruise_steps = 0;
        max_profile_speed = (int32_t)sqrtf(2.0f * accel * acceleration_steps);
    } else {
        cruise_steps = total_steps - acceleration_steps - deceleration_steps;
    }
    current_speed = x_motor_config.min_speed;
    motion_state = MOTION_ACCELERATING;
    // 设置定时器
    uint32_t interval = calculate_step_interval();
    timer_set_period(interval);
    timer_enable(true);
}

// 开始绘图
void motion_start_drawing() {
    if (path_count > 0 && (system_state == SYSTEM_RECEIVING_PATH || system_state == SYSTEM_IDLE)) {
        system_state = SYSTEM_DRAWING;
        motion_state = MOTION_IDLE;
        // 获取第一个目标点
        target_position = path_buffer[path_tail];
        path_tail = (path_tail + 1) % MAX_PATH_POINTS;
        path_count--;
        prepare_motion_to_target();
    }
}

// 紧急停止
void motion_emergency_stop() {
    system_state = SYSTEM_EMERGENCY_STOP;
    motion_state = MOTION_IDLE;
    timer_enable(false);
    laser_set_intensity(0);
}

// 获取当前系统状态
SystemState motion_get_system_state() {
    return system_state;
}

// 获取当前位置
Point motion_get_current_position() {
    return current_position;
}

// 更新当前速度：根据梯形或S曲线调整速度
static void update_current_speed() {
    switch (profile_type) {
        case PROFILE_TRAPEZOIDAL: {
            // 梯形速度控制
            if (motion_state == MOTION_ACCELERATING) {
                // 线性增加速度
                current_speed += (int32_t)x_motor_config.acceleration;
                if (current_speed > max_profile_speed) current_speed = max_profile_speed;
            } else if (motion_state == MOTION_DECELERATING) {
                // 线性减速
                current_speed -= (int32_t)x_motor_config.deceleration;
                if (current_speed < (int32_t)x_motor_config.min_speed) current_speed = (int32_t)x_motor_config.min_speed;
            }
            break;
        }
        case PROFILE_S_CURVE: {
            // 使用简单余弦缓动实现的S曲线速度控制
            // 参见 https://en.wikipedia.org/wiki/Ease_in_and_out#Sinusoidal_easing
            if (motion_state == MOTION_ACCELERATING) {
                float r = (acceleration_steps == 0) ? 1.0f : ((float)steps_completed / (float)acceleration_steps);
                if (r > 1.0f) r = 1.0f;
                float ease = 0.5f * (1.0f - cosf((float)M_PI * r));
                // 当前速度在最小速度到最大速度之间插值
                current_speed = (int32_t)(x_motor_config.min_speed + ease * (max_profile_speed - x_motor_config.min_speed));
            } else if (motion_state == MOTION_DECELERATING) {
                int32_t steps_left = total_steps - steps_completed;
                float r = (deceleration_steps == 0) ? 0.0f : ((float)steps_left / (float)deceleration_steps);
                if (r < 0.0f) r = 0.0f;
                if (r > 1.0f) r = 1.0f;
                float ease = 0.5f * (1.0f - cosf((float)M_PI * r));
                current_speed = (int32_t)(x_motor_config.min_speed + ease * (max_profile_speed - x_motor_config.min_speed));
            } else {
                // 巡航阶段保持最大速度
                current_speed = max_profile_speed;
            }
            break;
        }
        default:
            break;
    }
}

// 步进电机中断处理函数
void motion_step_handler() {
    if (system_state != SYSTEM_DRAWING || motion_state == MOTION_IDLE) {
        return;
    }
    // Bresenham 算法执行一步
    e2 = 2 * err;
    if (e2 > -dy) {
        err -= dy;
        current_position.x += sx;
        step_x_motor(sx > 0);
    }
    if (e2 < dx) {
        err += dx;
        current_position.y += sy;
        step_y_motor(sy > 0);
    }
    steps_completed++;
    // 检查是否到达目标
    if (steps_completed >= total_steps) {
        if (path_count > 0) {
            target_position = path_buffer[path_tail];
            path_tail = (path_tail + 1) % MAX_PATH_POINTS;
            path_count--;
            prepare_motion_to_target();
            return;
        } else {
            motion_state = MOTION_IDLE;
            system_state = SYSTEM_IDLE;
            timer_enable(false);
            laser_set_intensity(0);
            return;
        }
    }
    // 更新运动阶段
    if (motion_state == MOTION_ACCELERATING) {
        if (steps_completed >= acceleration_steps) {
            if (cruise_steps > 0) {
                motion_state = MOTION_CRUISING;
            } else {
                motion_state = MOTION_DECELERATING;
            }
        }
    } else if (motion_state == MOTION_CRUISING) {
        if (steps_completed >= total_steps - deceleration_steps) {
            motion_state = MOTION_DECELERATING;
        }
    }
    // 更新当前速度
    update_current_speed();
    // 更新定时器周期
    uint32_t interval = calculate_step_interval();
    timer_set_period(interval);
}