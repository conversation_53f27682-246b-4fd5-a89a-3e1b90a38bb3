#include "basic.h"
#include "uart_bsp.h"

// 全局变量定义
BasicControlMode_t g_basic_control_mode = BASIC_MODE_TRACKING;
int g_target_red_x = -1;  // 初始化为无效值，等待矩形检测
int g_target_red_y = -1;  // 初始化为无效值，等待矩形检测

// 私有变量
static ResetStatus_t reset_status = RESET_STATUS_IDLE;
static uint32_t reset_start_time = 0;
static uint32_t last_check_time = 0;

/**
 * @brief 初始化basic模块
 */
void basic_init(void)
{
    g_basic_control_mode = BASIC_MODE_TRACKING;

    // 只使用矩形中心作为原点，如果没有矩形数据则等待
    if (latest_rect_coord.isValid) {
        calculate_rect_center(latest_rect_coord.x, latest_rect_coord.y,&g_target_red_x, &g_target_red_y);
        my_printf(&huart1, "Basic module initialized. Origin: Rectangle center (%d, %d)\r\n",
                  g_target_red_x, g_target_red_y);
    } else {
        // 设置为无效值，表示还没有有效的原点
        g_target_red_x = -1;
        g_target_red_y = -1;
        my_printf(&huart1, "Basic module initialized. Origin: Waiting for rectangle detection\r\n");
    }

    reset_status = RESET_STATUS_IDLE;
    reset_start_time = 0;
    last_check_time = 0;
}

/**
 * @brief 设置控制模式
 * @param mode 控制模式
 */
void basic_set_mode(BasicControlMode_t mode)
{
    if (mode != g_basic_control_mode) {
        g_basic_control_mode = mode;
        
        switch (mode) {
            case BASIC_MODE_TRACKING:
                my_printf(&huart1, "Mode: TRACKING activated\r\n");
                break;
            case BASIC_MODE_RESET:
                my_printf(&huart1, "Mode: RESET activated\r\n");
                break;
            case BASIC_MODE_SCREEN_EDGE:
                my_printf(&huart1, "Mode: SCREEN_EDGE activated\r\n");
                break;
            case BASIC_MODE_A4_TAPE:
                my_printf(&huart1, "Mode: A4_TAPE activated\r\n");
                break;
            case BASIC_MODE_ROTATED_A4:
                my_printf(&huart1, "Mode: ROTATED_A4 activated\r\n");
                break;
            default:
                my_printf(&huart1, "Mode: UNKNOWN (%d)\r\n", mode);
                break;
        }
    }
}

/**
 * @brief 获取当前控制模式
 * @return 当前控制模式
 */
BasicControlMode_t basic_get_mode(void)
{
    return g_basic_control_mode;
}

/**
 * @brief 计算矩形对角线交点
 * @param rect_x 矩形四个角点的X坐标数组
 * @param rect_y 矩形四个角点的Y坐标数组
 * @param center_x 输出交点X坐标
 * @param center_y 输出交点Y坐标
 * @return 0:成功, -1:失败
 */
static int calculate_rect_center(int rect_x[4], int rect_y[4], int *center_x, int *center_y)
{
    // 计算矩形对角线交点（几何中心）
    // 方法：取所有四个点的平均值
    int sum_x = rect_x[0] + rect_x[1] + rect_x[2] + rect_x[3];
    int sum_y = rect_y[0] + rect_y[1] + rect_y[2] + rect_y[3];

    *center_x = sum_x / 4;
    *center_y = sum_y / 4;

    return 0;
}

/**
 * @brief 启动复位功能
 * @return 0:成功启动, -1:失败
 */
int basic_start_reset(void)
{
    // 检查红色激光数据是否有效（改为警告而非阻断）
    if (!latest_red_laser_coord.isValid) {
        my_printf(&huart1, "Warning: Red laser data not available, using default position (0,0)\r\n");
        my_printf(&huart1, "Reset will start from default position and adjust when laser data becomes available\r\n");
    } else {
        my_printf(&huart1, "Red laser current position: (%d, %d)\r\n",
                  latest_red_laser_coord.x, latest_red_laser_coord.y);
    }

    // 检查矩形坐标数据是否有效
    if (!latest_rect_coord.isValid) {
        my_printf(&huart1, "Reset failed: Rectangle data not available\r\n");
        my_printf(&huart1, "Please ensure rectangle is detected before reset\r\n");
        return -1;
    }

    // 计算矩形对角线交点作为复位目标
    calculate_rect_center(latest_rect_coord.x, latest_rect_coord.y,
                         &g_target_red_x, &g_target_red_y);

    my_printf(&huart1, "Rectangle detected: (%d,%d) (%d,%d) (%d,%d) (%d,%d)\r\n",
              latest_rect_coord.x[0], latest_rect_coord.y[0],
              latest_rect_coord.x[1], latest_rect_coord.y[1],
              latest_rect_coord.x[2], latest_rect_coord.y[2],
              latest_rect_coord.x[3], latest_rect_coord.y[3]);

    my_printf(&huart1, "Reset target: Rectangle center (%d, %d)\r\n",
              g_target_red_x, g_target_red_y);

    // 设置复位状态
    reset_status = RESET_STATUS_MOVING;
    reset_start_time = HAL_GetTick();
    last_check_time = reset_start_time;

    // 切换到复位模式
    basic_set_mode(BASIC_MODE_RESET);

    my_printf(&huart1, "Reset started. Current: (%d, %d) -> Target: (%d, %d)\r\n",
              latest_red_laser_coord.x, latest_red_laser_coord.y,
              g_target_red_x, g_target_red_y);

    return 0;
}

/**
 * @brief 获取复位状态
 * @return 复位状态
 */
ResetStatus_t basic_get_reset_status(void)
{
    return reset_status;
}

/**
 * @brief 检查复位是否完成
 * @return 1:完成, 0:未完成
 */
int basic_is_reset_completed(void)
{
    return (reset_status == RESET_STATUS_COMPLETED);
}

/**
 * @brief 计算两点之间的距离
 * @param x1, y1 第一个点的坐标
 * @param x2, y2 第二个点的坐标
 * @return 距离（像素）
 */
float basic_calculate_distance(int x1, int y1, int x2, int y2)
{
    int dx = x2 - x1;
    int dy = y2 - y1;
    return sqrtf((float)(dx * dx + dy * dy));
}

/**
 * @brief 复位状态检查和更新
 */
static void basic_update_reset_status(void)
{
    if (reset_status != RESET_STATUS_MOVING) {
        return;
    }
    
    uint32_t current_time = HAL_GetTick();
    
    // 检查超时
    if (current_time - reset_start_time > RESET_TIMEOUT_MS) {
        reset_status = RESET_STATUS_TIMEOUT;
        my_printf(&huart1, "Reset timeout! Duration: %lu ms\r\n", 
                  current_time - reset_start_time);
        return;
    }
    
    // 每500ms检查一次位置
    if (current_time - last_check_time >= 500) {
        last_check_time = current_time;

        if (latest_red_laser_coord.isValid) {
            float distance = basic_calculate_distance(
                latest_red_laser_coord.x, latest_red_laser_coord.y,
                g_target_red_x, g_target_red_y);

            my_printf(&huart1, "Reset progress: Current(%d,%d), Distance=%.1f px\r\n",
                      latest_red_laser_coord.x, latest_red_laser_coord.y, distance);

            // 检查是否到达目标位置
            if (distance <= RESET_ERROR_THRESHOLD) {
                reset_status = RESET_STATUS_COMPLETED;
                uint32_t duration = current_time - reset_start_time;
                my_printf(&huart1, "Reset completed! Duration: %lu ms, Final error: %.1f px\r\n",
                          duration, distance);
            }
        } else {
            // 红色激光数据无效，显示等待状态
            my_printf(&huart1, "Reset waiting: Red laser data not available, motors paused\r\n");
        }
    }
}

/**
 * @brief 更新原点位置（当检测到新的矩形时调用）
 */
void basic_update_origin(void)
{
    if (latest_rect_coord.isValid) {
        int new_x, new_y;
        calculate_rect_center(latest_rect_coord.x, latest_rect_coord.y, &new_x, &new_y);

        // 只有当位置发生显著变化时才更新（避免频繁更新）
        float distance = basic_calculate_distance(g_target_red_x, g_target_red_y, new_x, new_y);
        if (distance > 10.0f) { // 10像素阈值
            g_target_red_x = new_x;
            g_target_red_y = new_y;
            my_printf(&huart1, "Origin updated to rectangle center: (%d, %d)\r\n",
                      g_target_red_x, g_target_red_y);
        }
    }
}

/**
 * @brief basic模块处理函数（需要在主循环中调用）
 */
void basic_proc(void)
{
    // 根据当前模式执行相应的处理
    switch (g_basic_control_mode) {
        case BASIC_MODE_RESET:
            basic_update_reset_status();
            break;

        case BASIC_MODE_SCREEN_EDGE:
            // TODO: 实现屏幕边线模式
            break;

        case BASIC_MODE_A4_TAPE:
            // TODO: 实现A4胶带模式
            break;

        case BASIC_MODE_ROTATED_A4:
            // TODO: 实现旋转A4模式
            break;

        case BASIC_MODE_TRACKING:
        default:
            // 追踪模式下也可以更新原点位置
            basic_update_origin();
            break;
    }
}
