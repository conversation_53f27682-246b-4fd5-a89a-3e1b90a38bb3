#ifndef __IMPROVED_MOTION_EXAMPLE_H__
#define __IMPROVED_MOTION_EXAMPLE_H__

#include "improved_motion_controller.h"
#include "improved_motion_bsp.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

// 摄像头数据直驱模式函数声明
void improved_motion_example_init(void);
void improved_motion_example_process(void);
void process_camera_data(const char* data);

// 摄像头数据处理函数
void process_calibration_data(const char* data);
void process_path_data(const char* data);
void process_tracking_data(const char* data);

// 坐标转换函数
Point normalize_to_motor_coord(int32_t nx, int32_t ny);

#endif // __IMPROVED_MOTION_EXAMPLE_H__
