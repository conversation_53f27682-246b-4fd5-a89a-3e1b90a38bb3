#ifndef __IMPROVED_MOTION_EXAMPLE_H__
#define __IMPROVED_MOTION_EXAMPLE_H__

#include "improved_motion_controller.h"
#include "improved_motion_bsp.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

// 摄像头主控模式函数声明
void improved_motion_example_init(void);
void improved_motion_example_process(void);
void process_motion_command(const char* cmd);

// 摄像头协议处理函数
void process_calibration_data(const char* data);
void process_path_data(const char* data);
void process_control_command(const char* command);
void process_tracking_data(const char* data);

// 坐标转换和状态函数
Point normalize_to_motor_coord(int32_t nx, int32_t ny);
void send_status_to_camera(void);
void local_emergency_stop(void);

#endif // __IMPROVED_MOTION_EXAMPLE_H__
