#ifndef __IMPROVED_MOTION_EXAMPLE_H__
#define __IMPROVED_MOTION_EXAMPLE_H__

#include "improved_motion_controller.h"
#include "../bsp/improved_motion_bsp.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

// 函数声明
void improved_motion_example_init(void);
void draw_rectangle_example(void);
void draw_circle_example(void);
void switch_profile_example(void);
void get_status_example(void);
void emergency_stop_example(void);
void process_motion_command(const char* cmd);
void improved_motion_example_process(void);

// 辅助函数
void generate_circle_path(int32_t center_x, int32_t center_y, int32_t radius);

#endif // __IMPROVED_MOTION_EXAMPLE_H__
