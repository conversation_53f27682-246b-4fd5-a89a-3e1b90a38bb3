#include "improved_motion_bsp.h"

/* 静态变量 */
static bool timer_enabled = false;
static uint32_t current_period = MOTION_MAX_PERIOD;

/**
 * @brief 改进运动控制器BSP初始化
 */
void improved_motion_bsp_init(void)
{
    improved_motion_gpio_init();
    improved_motion_timer_init();
    improved_motion_laser_init();
    
    // 初始状态：定时器禁用，激光关闭
    timer_enable(false);
    laser_set_intensity(0);
}

/**
 * @brief GPIO初始化
 */
void improved_motion_gpio_init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIO时钟
    __HAL_RCC_GPIOB_CLK_ENABLE();
    
    // 配置X轴步进电机引脚
    GPIO_InitStruct.Pin = X_MOTOR_STEP_PIN | X_MOTOR_DIR_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(X_MOTOR_STEP_PORT, &GPIO_InitStruct);
    
    // 配置Y轴步进电机引脚
    GPIO_InitStruct.Pin = Y_MOTOR_STEP_PIN | Y_MOTOR_DIR_PIN;
    HAL_GPIO_Init(Y_MOTOR_STEP_PORT, &GPIO_InitStruct);
    
    // 配置激光使能引脚
    GPIO_InitStruct.Pin = LASER_ENABLE_PIN;
    HAL_GPIO_Init(LASER_ENABLE_PORT, &GPIO_InitStruct);
    
    // 初始状态：所有输出为低电平
    HAL_GPIO_WritePin(X_MOTOR_STEP_PORT, X_MOTOR_STEP_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(X_MOTOR_DIR_PORT, X_MOTOR_DIR_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Y_MOTOR_STEP_PORT, Y_MOTOR_STEP_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(Y_MOTOR_DIR_PORT, Y_MOTOR_DIR_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(LASER_ENABLE_PORT, LASER_ENABLE_PIN, GPIO_PIN_RESET);
}

/**
 * @brief 定时器初始化
 */
void improved_motion_timer_init(void)
{
    // 注意：这里假设TIM1已经在main.c中初始化
    // 我们只需要配置中断和周期
    
    // 设置初始周期为最大值（禁用状态）
    __HAL_TIM_SET_AUTORELOAD(&htim1, MOTION_MAX_PERIOD);
    __HAL_TIM_SET_COUNTER(&htim1, 0);
    
    // 使能定时器更新中断
    HAL_TIM_Base_Start_IT(&htim1);
}

/**
 * @brief 激光PWM初始化
 */
void improved_motion_laser_init(void)
{
    // 注意：这里假设TIM3已经在main.c中初始化为PWM模式
    // 启动PWM输出
    HAL_TIM_PWM_Start(&htim3, MOTION_LASER_PWM_CHANNEL);
    
    // 初始占空比为0（激光关闭）
    __HAL_TIM_SET_COMPARE(&htim3, MOTION_LASER_PWM_CHANNEL, 0);
}

/**
 * @brief 设置定时器周期
 * @param period 定时器周期值
 */
void timer_set_period(uint32_t period)
{
    // 限制周期范围
    if (period < MOTION_MIN_PERIOD) {
        period = MOTION_MIN_PERIOD;
    } else if (period > MOTION_MAX_PERIOD) {
        period = MOTION_MAX_PERIOD;
    }
    
    current_period = period;
    
    // 更新定时器自动重装载值
    __HAL_TIM_SET_AUTORELOAD(&htim1, period);
    __HAL_TIM_SET_COUNTER(&htim1, 0);
}

/**
 * @brief 启用/禁用定时器
 * @param enable true-启用，false-禁用
 */
void timer_enable(bool enable)
{
    timer_enabled = enable;
    
    if (enable) {
        // 启用定时器中断
        __HAL_TIM_ENABLE_IT(&htim1, TIM_IT_UPDATE);
        HAL_TIM_Base_Start_IT(&htim1);
    } else {
        // 禁用定时器中断
        __HAL_TIM_DISABLE_IT(&htim1, TIM_IT_UPDATE);
        HAL_TIM_Base_Stop_IT(&htim1);
    }
}

/**
 * @brief X轴步进电机步进
 * @param dir 方向，true-正方向，false-负方向
 */
void step_x_motor(bool dir)
{
    // 设置方向
    HAL_GPIO_WritePin(X_MOTOR_DIR_PORT, X_MOTOR_DIR_PIN, dir ? GPIO_PIN_SET : GPIO_PIN_RESET);
    
    // 产生步进脉冲
    HAL_GPIO_WritePin(X_MOTOR_STEP_PORT, X_MOTOR_STEP_PIN, GPIO_PIN_SET);
    // 短暂延时确保脉冲宽度
    for(volatile int i = 0; i < 10; i++);
    HAL_GPIO_WritePin(X_MOTOR_STEP_PORT, X_MOTOR_STEP_PIN, GPIO_PIN_RESET);
}

/**
 * @brief Y轴步进电机步进
 * @param dir 方向，true-正方向，false-负方向
 */
void step_y_motor(bool dir)
{
    // 设置方向
    HAL_GPIO_WritePin(Y_MOTOR_DIR_PORT, Y_MOTOR_DIR_PIN, dir ? GPIO_PIN_SET : GPIO_PIN_RESET);
    
    // 产生步进脉冲
    HAL_GPIO_WritePin(Y_MOTOR_STEP_PORT, Y_MOTOR_STEP_PIN, GPIO_PIN_SET);
    // 短暂延时确保脉冲宽度
    for(volatile int i = 0; i < 10; i++);
    HAL_GPIO_WritePin(Y_MOTOR_STEP_PORT, Y_MOTOR_STEP_PIN, GPIO_PIN_RESET);
}

/**
 * @brief 设置激光强度
 * @param intensity 激光强度，0-LASER_MAX_INTENSITY
 */
void laser_set_intensity(uint16_t intensity)
{
    // 限制强度范围
    if (intensity > LASER_MAX_INTENSITY) {
        intensity = LASER_MAX_INTENSITY;
    }
    
    // 设置PWM占空比
    __HAL_TIM_SET_COMPARE(&htim3, MOTION_LASER_PWM_CHANNEL, intensity);
    
    // 控制激光使能引脚
    HAL_GPIO_WritePin(LASER_ENABLE_PORT, LASER_ENABLE_PIN, 
                      intensity > 0 ? GPIO_PIN_SET : GPIO_PIN_RESET);
}

/**
 * @brief 串口发送数据 (使用USART6替代UART5)
 * @param msg 要发送的消息
 */
void uart_send(const char* msg)
{
    if (msg != NULL) {
        HAL_UART_Transmit(&MOTION_UART_INSTANCE, (uint8_t*)msg, strlen(msg), HAL_MAX_DELAY);
    }
}

/**
 * @brief 获取系统时钟
 * @return 系统时钟值（毫秒）
 */
uint32_t get_tick(void)
{
    return HAL_GetTick();
}

/**
 * @brief 定时器中断处理函数
 * 这个函数应该在stm32f4xx_it.c中的TIM1_UP_TIM10_IRQHandler中调用
 */
void motion_timer_irq_handler(void)
{
    if (timer_enabled) {
        // 调用运动控制器的步进处理函数
        motion_step_handler();
    }
}
