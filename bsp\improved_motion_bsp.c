#include "improved_motion_bsp.h"
#include <math.h>
#include <stdio.h>

/* 静态变量 */
static bool timer_enabled = false;
static uint32_t current_period = MOTION_MAX_PERIOD;

/* 电机控制变量 */
static float current_x_rpm = 0.0f;
static float current_y_rpm = 0.0f;
static int32_t x_step_accumulator = 0;
static int32_t y_step_accumulator = 0;
static uint32_t last_motor_update_time = 0;

/* 激光控制变量 (软件实现) */
static uint16_t current_laser_intensity = 0;
static bool laser_enabled = false;

/**
 * @brief 改进运动控制器BSP初始化
 */
void improved_motion_bsp_init(void)
{
    improved_motion_timer_init();
    improved_motion_motor_init();

    // 初始化变量
    current_x_rpm = 0.0f;
    current_y_rpm = 0.0f;
    x_step_accumulator = 0;
    y_step_accumulator = 0;
    last_motor_update_time = 0;
    current_laser_intensity = 0;
    laser_enabled = false;

    // 初始状态：定时器禁用，激光关闭
    timer_enable(false);
    laser_set_intensity(0);

    uart_send("Improved Motion BSP initialized with existing motor system\r\n");
}

/**
 * @brief 电机初始化 (使用现有的电机控制系统)
 */
void improved_motion_motor_init(void)
{
    // 电机初始化已在 Step_Motor_Init() 中完成
    // 这里只需要确保电机处于停止状态
    Step_Motor_Stop();

    uart_send("Motors initialized using existing Emm_V5 system\r\n");
}

/**
 * @brief 定时器初始化
 */
void improved_motion_timer_init(void)
{
    // 注意：这里假设TIM1已经在main.c中初始化
    // 设置定时器用于运动控制更新，频率较低 (1kHz)

    uint32_t period = MOTION_TIMER_FREQ / MOTION_UPDATE_FREQ - 1;
    __HAL_TIM_SET_AUTORELOAD(&htim1, period);
    __HAL_TIM_SET_COUNTER(&htim1, 0);

    // 使能定时器更新中断
    HAL_TIM_Base_Start_IT(&htim1);

    uart_send("Motion timer initialized for motor control updates\r\n");
}

/**
 * @brief 设置定时器周期
 * @param period 定时器周期值
 */
void timer_set_period(uint32_t period)
{
    // 限制周期范围
    if (period < MOTION_MIN_PERIOD) {
        period = MOTION_MIN_PERIOD;
    } else if (period > MOTION_MAX_PERIOD) {
        period = MOTION_MAX_PERIOD;
    }
    
    current_period = period;
    
    // 更新定时器自动重装载值
    __HAL_TIM_SET_AUTORELOAD(&htim1, period);
    __HAL_TIM_SET_COUNTER(&htim1, 0);
}

/**
 * @brief 启用/禁用定时器
 * @param enable true-启用，false-禁用
 */
void timer_enable(bool enable)
{
    timer_enabled = enable;
    
    if (enable) {
        // 启用定时器中断
        __HAL_TIM_ENABLE_IT(&htim1, TIM_IT_UPDATE);
        HAL_TIM_Base_Start_IT(&htim1);
    } else {
        // 禁用定时器中断
        __HAL_TIM_DISABLE_IT(&htim1, TIM_IT_UPDATE);
        HAL_TIM_Base_Stop_IT(&htim1);
    }
}

/**
 * @brief X轴步进电机步进 (使用累积器方式)
 * @param dir 方向，true-正方向，false-负方向
 */
void step_x_motor(bool dir)
{
    // 累积步进方向和数量
    if (dir) {
        x_step_accumulator++;
    } else {
        x_step_accumulator--;
    }
}

/**
 * @brief Y轴步进电机步进 (使用累积器方式)
 * @param dir 方向，true-正方向，false-负方向
 */
void step_y_motor(bool dir)
{
    // 累积步进方向和数量
    if (dir) {
        y_step_accumulator++;
    } else {
        y_step_accumulator--;
    }
}

/**
 * @brief 设置激光强度 (软件实现，激光绑定在Y轴电机上)
 * @param intensity 激光强度，0-LASER_MAX_INTENSITY
 */
void laser_set_intensity(uint16_t intensity)
{
    // 限制强度范围
    if (intensity > LASER_MAX_INTENSITY) {
        intensity = LASER_MAX_INTENSITY;
    }

    current_laser_intensity = intensity;
    laser_enabled = (intensity > 0);

    // 激光绑定在Y轴电机上，这里只是软件标志
    // 实际的激光控制可能通过Y轴电机的特殊功能实现
    // 或者在调试时输出激光状态信息
    if (laser_enabled) {
        char msg[64];
        snprintf(msg, sizeof(msg), "Laser ON: intensity=%d%%\r\n",
                 (intensity * 100) / LASER_MAX_INTENSITY);
        uart_send(msg);
    } else {
        uart_send("Laser OFF\r\n");
    }
}

/**
 * @brief 串口发送数据 (使用USART6替代UART5)
 * @param msg 要发送的消息
 */
void uart_send(const char* msg)
{
    if (msg != NULL) {
        HAL_UART_Transmit(&MOTION_UART_INSTANCE, (uint8_t*)msg, strlen(msg), HAL_MAX_DELAY);
    }
}

/**
 * @brief 获取系统时钟
 * @return 系统时钟值（毫秒）
 */
uint32_t get_tick(void)
{
    return HAL_GetTick();
}

/**
 * @brief 更新电机速度 (将累积的步进转换为速度命令)
 */
static void update_motor_speeds(void)
{
    uint32_t current_time = get_tick();
    uint32_t time_diff = current_time - last_motor_update_time;

    // 至少间隔10ms才更新一次电机速度，避免频繁发送命令
    if (time_diff < 10) {
        return;
    }

    // 计算基于累积器的目标速度 (RPM)
    // 假设每个步进对应 1.8度，200步/转
    float time_factor = 60000.0f / (time_diff * MOTION_STEPS_PER_REV); // 转换为RPM

    float target_x_rpm = x_step_accumulator * time_factor;
    float target_y_rpm = y_step_accumulator * time_factor;

    // 限制速度范围
    if (target_x_rpm > MOTION_MAX_RPM) target_x_rpm = MOTION_MAX_RPM;
    if (target_x_rpm < -MOTION_MAX_RPM) target_x_rpm = -MOTION_MAX_RPM;
    if (target_y_rpm > MOTION_MAX_RPM) target_y_rpm = MOTION_MAX_RPM;
    if (target_y_rpm < -MOTION_MAX_RPM) target_y_rpm = -MOTION_MAX_RPM;

    // 只有速度变化较大时才发送新的控制命令
    float x_diff = target_x_rpm - current_x_rpm;
    float y_diff = target_y_rpm - current_y_rpm;

    if (fabs(x_diff) > 0.1f || fabs(y_diff) > 0.1f) {
        // 使用现有的电机控制函数
        Step_Motor_Set_Speed_my(target_x_rpm, target_y_rpm);

        current_x_rpm = target_x_rpm;
        current_y_rpm = target_y_rpm;

        // 调试输出 (可选)
        // char msg[128];
        // snprintf(msg, sizeof(msg), "Motor speeds: X=%.1f RPM, Y=%.1f RPM\r\n",
        //          target_x_rpm, target_y_rpm);
        // uart_send(msg);
    }

    // 重置累积器和时间
    x_step_accumulator = 0;
    y_step_accumulator = 0;
    last_motor_update_time = current_time;
}

/**
 * @brief 定时器中断处理函数
 * 这个函数应该在stm32f4xx_it.c中的TIM1_UP_TIM10_IRQHandler中调用
 */
void motion_timer_irq_handler(void)
{
    if (timer_enabled) {
        // 调用运动控制器的步进处理函数
        motion_step_handler();

        // 更新电机速度
        update_motor_speeds();
    }
}
