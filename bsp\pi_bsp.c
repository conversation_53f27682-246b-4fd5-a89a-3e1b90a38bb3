#include "pi_bsp.h"
#include "basic.h"
#include "step_motor_bsp.h"
#include "../app/mypid.h"

// 默认值设为无效状态，X, Y 为 0
LaserCoord_t latest_red_laser_coord = {RED_LASER_ID, 0, 0, 0};
LaserCoord_t latest_green_laser_coord = {GREEN_LASER_ID, 0, 0, 0};

// 新增：矩形坐标和四点控制数据的全局变量定义
RectCoord_t latest_rect_coord = {0};
FourPointCoord_t latest_four_point_coord = {0};

// 扩展的MaixCam数据解析函数
// 支持格式：red:(x,y), gre:(x,y), rect:(x1,y1,x2,y2,x3,y3,x4,y4), point:(index,x,y)
int pi_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // 空指针检查

    int parsed_count;

    // 尝试匹配 "red:(x,y)" 格式
    if (strncmp(buffer, "red:", 4) == 0)
    {
        int parsed_x, parsed_y;
        parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
            return -2; // 解析失败

        // 解析成功，更新全局红色激光坐标
        latest_red_laser_coord.x = parsed_x;
        latest_red_laser_coord.y = parsed_y;
        latest_red_laser_coord.isValid = 1;

        my_printf(&huart1, "Parsed RED: X=%d, Y=%d\r\n", latest_red_laser_coord.x, latest_red_laser_coord.y);
    }
    // 尝试匹配 "gre:(x,y)" 格式
    else if (strncmp(buffer, "gre:", 4) == 0)
    {
        int parsed_x, parsed_y;
        parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
            return -2; // 解析失败

        // 解析成功，更新全局绿色激光坐标
        latest_green_laser_coord.x = parsed_x;
        latest_green_laser_coord.y = parsed_y;
        latest_green_laser_coord.isValid = 1;

        my_printf(&huart1, "Parsed GRE: X=%d, Y=%d\r\n", latest_green_laser_coord.x, latest_green_laser_coord.y);
    }
    // 新增：尝试匹配 "rect:(x1,y1,x2,y2,x3,y3,x4,y4)" 格式
    else if (strncmp(buffer, "rect:", 5) == 0)
    {
        my_printf(&huart1, "Attempting to parse RECT data: '%s'\r\n", buffer);
        parsed_count = sscanf(buffer, "rect:(%d,%d,%d,%d,%d,%d,%d,%d)",
                             &latest_rect_coord.x[0], &latest_rect_coord.y[0],
                             &latest_rect_coord.x[1], &latest_rect_coord.y[1],
                             &latest_rect_coord.x[2], &latest_rect_coord.y[2],
                             &latest_rect_coord.x[3], &latest_rect_coord.y[3]);

        my_printf(&huart1, "RECT sscanf parsed %d values\r\n", parsed_count);

        if (parsed_count != 8) {
            my_printf(&huart1, "RECT parse failed: expected 8 values, got %d\r\n", parsed_count);
            return -2; // 解析失败
        }

        latest_rect_coord.isValid = 1;
        my_printf(&huart1, "Parsed RECT: (%d,%d) (%d,%d) (%d,%d) (%d,%d)\r\n",
                  latest_rect_coord.x[0], latest_rect_coord.y[0],
                  latest_rect_coord.x[1], latest_rect_coord.y[1],
                  latest_rect_coord.x[2], latest_rect_coord.y[2],
                  latest_rect_coord.x[3], latest_rect_coord.y[3]);

        // 自动更新原点位置
        basic_update_origin();
    }
    // 新增：尝试匹配 "point:(index,x,y)" 格式
    else if (strncmp(buffer, "point:", 6) == 0)
    {
        parsed_count = sscanf(buffer, "point:(%d,%d,%d)",
                             &latest_four_point_coord.index,
                             &latest_four_point_coord.x,
                             &latest_four_point_coord.y);
        if (parsed_count != 3)
            return -2; // 解析失败

        latest_four_point_coord.isValid = 1;
        my_printf(&huart1, "Parsed POINT: Index=%d, X=%d, Y=%d\r\n",
                  latest_four_point_coord.index,
                  latest_four_point_coord.x,
                  latest_four_point_coord.y);
    }
    else
    {
        // 未知格式或无效数据
        my_printf(&huart1, "Unknown data format: '%s'\r\n", buffer);
        return -3; // 未知或无效格式
    }

    return 0; // 成功
}



void pi_proc(void)
{
	float pos_out_x, pos_out_y = 0;

	// 调用basic模块处理函数
	basic_proc();

	// 根据控制模式选择不同的控制逻辑
	switch (basic_get_mode()) {
		case BASIC_MODE_TRACKING:
			// 原有的追踪模式：绿色激光追踪红色激光
			pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
			pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
			break;

		case BASIC_MODE_RESET:
		case BASIC_MODE_SCREEN_EDGE:
		case BASIC_MODE_A4_TAPE:
		case BASIC_MODE_ROTATED_A4:
			// 目标位置控制模式：红色激光移动到目标位置
			if (latest_red_laser_coord.isValid) {
				// 红色激光数据有效，正常PID控制
				pos_out_x = pid_calc(&pid_x, latest_red_laser_coord.x, g_target_red_x, 0);
				pos_out_y = pid_calc(&pid_y, latest_red_laser_coord.y, g_target_red_y, 0);
			} else {
				// 红色激光数据无效，暂停电机控制，等待数据
				pos_out_x = 0;
				pos_out_y = 0;
			}
			break;

		default:
			// 默认使用追踪模式
			pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
			pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
			break;
	}

	Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
}

