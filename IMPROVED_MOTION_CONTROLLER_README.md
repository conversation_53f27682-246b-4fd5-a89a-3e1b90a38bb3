# Improved Motion Controller 配置说明

## 概述

本文档说明了如何配置和使用 `improved_motion_controller` 模块。该模块提供了改进的运动控制算法，支持梯形和S曲线加减速，可以有效减少机械振动并提升绘图质量。

## 主要特性

- **双运动曲线支持**：梯形速度曲线和S曲线速度曲线
- **路径缓冲**：支持多点路径规划
- **Bresenham算法**：精确的直线插补
- **实时速度控制**：动态调整运动速度
- **紧急停止**：安全的运动中断机制

## 文件结构

```
app/
├── improved_motion_controller.c    # 核心运动控制算法
├── improved_motion_controller.h    # 接口定义
├── improved_motion_example.c       # 使用示例
└── improved_motion_example.h       # 示例接口

bsp/
├── improved_motion_bsp.c           # 硬件抽象层实现
└── improved_motion_bsp.h           # 硬件接口定义
```

## 硬件配置

### 电机系统集成

**重要**：本系统已完全集成现有的 Emm_V5 电机控制系统，无需额外的GPIO配置。

```c
// 使用现有的电机配置
#define MOTION_X_MOTOR_UART         MOTOR_X_UART    // X轴电机串口 (huart2)
#define MOTION_Y_MOTOR_UART         MOTOR_Y_UART    // Y轴电机串口 (huart4)
#define MOTION_X_MOTOR_ADDR         MOTOR_X_ADDR    // X轴电机地址 (0x01)
#define MOTION_Y_MOTOR_ADDR         MOTOR_Y_ADDR    // Y轴电机地址 (0x01)
```

### 串口配置变更

根据用户要求，通信串口已从 **UART5** 更改为 **USART6**：

```c
// 在 improved_motion_bsp.h 中
#define MOTION_UART_INSTANCE        huart6    // 使用USART6
```

### 激光控制

激光系统绑定在Y轴电机上，采用软件控制方式：
- 不需要额外的GPIO引脚
- 激光强度通过软件标志控制
- 激光状态通过串口输出调试信息

### 定时器配置

- **TIM1**：运动控制更新定时器 (1kHz)，用于协调步进算法和电机控制
- 不再需要直接的步进脉冲生成

## 软件配置

### 1. 初始化配置

在 `main.c` 中已添加完整的初始化代码：

```c
// 初始化BSP
improved_motion_bsp_init();

// 配置电机参数
MotorConfig x_config = {
    .steps_per_mm = 200,      // 每毫米步数
    .max_speed = 1000,        // 最大速度 (步/秒)
    .acceleration = 500,      // 加速度 (步/秒²)
    .deceleration = 500,      // 减速度 (步/秒²)
    .min_speed = 50           // 最小速度
};

// 初始化运动控制器
improved_motion_controller_init(&x_config, &y_config, PROFILE_S_CURVE);
```

### 2. 中断配置

在 `stm32f4xx_it.c` 中已添加TIM1中断处理：

```c
void TIM1_UP_TIM10_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&htim1);
    motion_timer_irq_handler();  // 调用运动控制器中断处理
}
```

## 使用方法

### 串口命令接口

通过串口发送以下命令来控制运动：

```
motion_help     - 显示帮助信息
motion_rect     - 绘制矩形
motion_circle   - 绘制圆形
motion_switch   - 切换运动曲线类型
motion_status   - 获取系统状态
motion_stop     - 紧急停止
```

### 编程接口

```c
// 添加路径点
Point point = {100, 100};
motion_add_path_point(point);

// 开始绘图
motion_start_drawing();

// 切换运动曲线类型
motion_set_profile_type(PROFILE_TRAPEZOIDAL);

// 获取当前位置
Point current_pos = motion_get_current_position();

// 紧急停止
motion_emergency_stop();
```

## 运动曲线类型

### 梯形速度曲线 (PROFILE_TRAPEZOIDAL)
- 传统的梯形速度控制
- 加速度恒定，适合快速运动
- 可能产生较大的机械振动

### S曲线速度曲线 (PROFILE_S_CURVE)
- 使用余弦缓动的S曲线控制
- 加速度平滑变化，减少振动
- 适合高精度绘图应用

## 系统状态

```c
typedef enum {
    SYSTEM_IDLE,              // 空闲状态
    SYSTEM_RECEIVING_PATH,    // 接收路径数据
    SYSTEM_DRAWING,           // 正在绘图
    SYSTEM_EMERGENCY_STOP     // 紧急停止
} SystemState;
```

## 注意事项

1. **电机系统兼容性**：完全使用现有的 Emm_V5 电机控制系统，无需修改硬件连接
2. **定时器频率**：TIM1配置为1kHz更新频率，用于协调算法和电机控制
3. **速度转换**：步进算法的输出通过累积器转换为电机速度命令
4. **串口冲突**：USART6同时用于MaixCam通信，使用时需注意数据冲突
5. **激光控制**：激光绑定在Y轴电机上，通过软件标志控制，无需额外硬件

## 故障排除

1. **电机不动**：
   - 检查现有的 Step_Motor_Init() 是否正常执行
   - 确认 Emm_V5 电机驱动器连接正常
   - 检查电机地址和串口配置

2. **运动不平滑**：
   - 调整 improved_motion_controller 的电机配置参数
   - 检查速度转换算法的累积器逻辑
   - 确认定时器更新频率合适

3. **串口无响应**：
   - 确认USART6配置正确，检查波特率设置
   - 注意USART6与MaixCam的数据冲突

4. **激光控制异常**：
   - 检查软件标志的设置
   - 确认激光与Y轴电机的绑定关系

5. **速度控制不准确**：
   - 检查累积器到RPM的转换公式
   - 调整 MOTION_STEPS_PER_REV 参数

## 扩展功能

可以根据需要扩展以下功能：
- 多轴联动控制
- 复杂路径规划算法
- 实时速度调整
- 位置反馈控制
- 激光功率动态调整
