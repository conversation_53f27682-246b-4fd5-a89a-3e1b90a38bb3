# Improved Motion Controller 配置说明

## 概述

本文档说明了如何配置和使用 `improved_motion_controller` 模块。该模块提供了改进的运动控制算法，支持梯形和S曲线加减速，可以有效减少机械振动并提升绘图质量。

## 主要特性

- **双运动曲线支持**：梯形速度曲线和S曲线速度曲线
- **路径缓冲**：支持多点路径规划
- **Bresenham算法**：精确的直线插补
- **实时速度控制**：动态调整运动速度
- **紧急停止**：安全的运动中断机制

## 文件结构

```
app/
├── improved_motion_controller.c    # 核心运动控制算法
├── improved_motion_controller.h    # 接口定义
├── improved_motion_example.c       # 使用示例
└── improved_motion_example.h       # 示例接口

bsp/
├── improved_motion_bsp.c           # 硬件抽象层实现
└── improved_motion_bsp.h           # 硬件接口定义
```

## 硬件配置

### 串口配置变更

根据用户要求，通信串口已从 **UART5** 更改为 **USART6**：

```c
// 在 improved_motion_bsp.h 中
#define MOTION_UART_INSTANCE        huart6    // 使用USART6
```

### GPIO引脚分配

```c
// X轴步进电机
#define X_MOTOR_STEP_PORT           GPIOB
#define X_MOTOR_STEP_PIN            GPIO_PIN_0
#define X_MOTOR_DIR_PORT            GPIOB  
#define X_MOTOR_DIR_PIN             GPIO_PIN_1

// Y轴步进电机
#define Y_MOTOR_STEP_PORT           GPIOB
#define Y_MOTOR_STEP_PIN            GPIO_PIN_2
#define Y_MOTOR_DIR_PORT            GPIOB
#define Y_MOTOR_DIR_PIN             GPIO_PIN_3

// 激光控制
#define LASER_ENABLE_PORT           GPIOB
#define LASER_ENABLE_PIN            GPIO_PIN_4
```

### 定时器配置

- **TIM1**：步进电机定时器，用于精确的步进控制
- **TIM3**：激光PWM定时器，用于激光强度控制

## 软件配置

### 1. 初始化配置

在 `main.c` 中已添加完整的初始化代码：

```c
// 初始化BSP
improved_motion_bsp_init();

// 配置电机参数
MotorConfig x_config = {
    .steps_per_mm = 200,      // 每毫米步数
    .max_speed = 1000,        // 最大速度 (步/秒)
    .acceleration = 500,      // 加速度 (步/秒²)
    .deceleration = 500,      // 减速度 (步/秒²)
    .min_speed = 50           // 最小速度
};

// 初始化运动控制器
improved_motion_controller_init(&x_config, &y_config, PROFILE_S_CURVE);
```

### 2. 中断配置

在 `stm32f4xx_it.c` 中已添加TIM1中断处理：

```c
void TIM1_UP_TIM10_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&htim1);
    motion_timer_irq_handler();  // 调用运动控制器中断处理
}
```

## 使用方法

### 串口命令接口

通过串口发送以下命令来控制运动：

```
motion_help     - 显示帮助信息
motion_rect     - 绘制矩形
motion_circle   - 绘制圆形
motion_switch   - 切换运动曲线类型
motion_status   - 获取系统状态
motion_stop     - 紧急停止
```

### 编程接口

```c
// 添加路径点
Point point = {100, 100};
motion_add_path_point(point);

// 开始绘图
motion_start_drawing();

// 切换运动曲线类型
motion_set_profile_type(PROFILE_TRAPEZOIDAL);

// 获取当前位置
Point current_pos = motion_get_current_position();

// 紧急停止
motion_emergency_stop();
```

## 运动曲线类型

### 梯形速度曲线 (PROFILE_TRAPEZOIDAL)
- 传统的梯形速度控制
- 加速度恒定，适合快速运动
- 可能产生较大的机械振动

### S曲线速度曲线 (PROFILE_S_CURVE)
- 使用余弦缓动的S曲线控制
- 加速度平滑变化，减少振动
- 适合高精度绘图应用

## 系统状态

```c
typedef enum {
    SYSTEM_IDLE,              // 空闲状态
    SYSTEM_RECEIVING_PATH,    // 接收路径数据
    SYSTEM_DRAWING,           // 正在绘图
    SYSTEM_EMERGENCY_STOP     // 紧急停止
} SystemState;
```

## 注意事项

1. **定时器精度**：确保TIM1配置为72MHz时钟，以获得精确的步进控制
2. **GPIO速度**：步进电机GPIO配置为高速模式，确保脉冲质量
3. **中断优先级**：TIM1中断应设置适当的优先级，避免被其他中断打断
4. **串口冲突**：USART6同时用于MaixCam通信，使用时需注意数据冲突

## 故障排除

1. **电机不动**：检查GPIO配置和定时器初始化
2. **运动不平滑**：调整电机配置参数，特别是加速度和最小速度
3. **串口无响应**：确认USART6配置正确，检查波特率设置
4. **系统卡死**：检查中断处理函数，确保没有死循环

## 扩展功能

可以根据需要扩展以下功能：
- 多轴联动控制
- 复杂路径规划算法
- 实时速度调整
- 位置反馈控制
- 激光功率动态调整
