// improved_motion_controller.h
//
// 改进的运动控制器接口，支持梯形和S曲线速度控制。该接口在原始
// motion_controller.h 的基础上增加了运动曲线类型配置，以便
// 使用更平滑的S曲线加减速，以减小机械振动并提升绘图质量。
// 文献指出，传统的梯形速度曲线在加速和减速阶段存在瞬时加速度
// 变化，产生较大的振动，而S曲线通过在加速度和减速度之间加入
// 过渡段显著减小振动【593956478117033†L110-L146】。因此在某些高精度或
// 需要减少震动的应用中，S曲线更为合适。

#ifndef IMPROVED_MOTION_CONTROLLER_H
#define IMPROVED_MOTION_CONTROLLER_H

#include <stdint.h>
#include <stdbool.h>

// 坐标点结构
typedef struct {
    int32_t x;
    int32_t y;
} Point;

// 步进电机配置
typedef struct {
    uint32_t steps_per_mm;    // 每毫米步数
    uint32_t max_speed;       // 最大速度 (步/秒)
    uint32_t acceleration;    // 加速度 (步/秒²)
    uint32_t deceleration;    // 减速度 (步/秒²)
    uint32_t min_speed;       // 最小速度，避免速度过低导致堵转
} MotorConfig;

// 运动状态
typedef enum {
    MOTION_IDLE,
    MOTION_ACCELERATING,
    MOTION_CRUISING,
    MOTION_DECELERATING
} MotionState;

// 系统状态
typedef enum {
    SYSTEM_IDLE,
    SYSTEM_RECEIVING_PATH,
    SYSTEM_DRAWING,
    SYSTEM_EMERGENCY_STOP
} SystemState;

// 运动曲线类型
typedef enum {
    PROFILE_TRAPEZOIDAL,  // 传统梯形速度曲线
    PROFILE_S_CURVE       // S曲线速度曲线
} MotionProfileType;

// 初始化运动控制器
// 参数 profile_type 用于选择梯形或S曲线控制算法
void improved_motion_controller_init(const MotorConfig* x_config,
                                     const MotorConfig* y_config,
                                     MotionProfileType profile_type);

// 设置运动曲线类型，在运行期间动态切换
void motion_set_profile_type(MotionProfileType type);

// 添加路径点
void motion_add_path_point(Point point);

// 开始绘图
void motion_start_drawing();

// 紧急停止
void motion_emergency_stop();

// 获取当前系统状态
SystemState motion_get_system_state();

// 获取当前坐标
Point motion_get_current_position();

// 步进电机中断处理函数 (在定时器中断中调用)
void motion_step_handler();

#endif // IMPROVED_MOTION_CONTROLLER_H