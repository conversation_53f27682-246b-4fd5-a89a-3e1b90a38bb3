#!/usr/bin/env python3
# camera_controller_example.py
# 
# 摄像头主控模式示例代码
# 演示如何控制重构后的STM32激光画图系统

from maix import image, display, app, time, camera
import cv2
import numpy as np
import gc
from micu_uart_lib import (
    SimpleUART, micu_printf, bind_variable,
    VariableContainer, clear_variable_bindings
)

# --------------------------- 激光画图主控制器类 ---------------------------
class LaserDrawingMasterController:
    def __init__(self):
        self.drawing_state = "IDLE"  # IDLE, CALIBRATED, PATH_LOADED, DRAWING
        self.a4_corners = None       # A4纸四角坐标
        self.transform_matrix = None # 透视变换矩阵
        self.current_pattern = "triangle"
        self.pattern_scale = 0.3
        self.pattern_position = (0.5, 0.5)
        
        # 预定义图形生成器
        self.patterns = {
            "triangle": self.generate_triangle,
            "rectangle": self.generate_rectangle,
            "circle": self.generate_circle,
            "star": self.generate_star,
            "heart": self.generate_heart
        }
        
    def set_a4_corners(self, corners):
        """设置A4纸四角坐标并发送校准数据"""
        self.a4_corners = corners
        # 计算透视变换矩阵
        src_pts = np.array(corners, dtype=np.float32)
        dst_pts = np.array([[0, 0], [1, 0], [1, 1], [0, 1]], dtype=np.float32)
        self.transform_matrix = cv2.getPerspectiveTransform(src_pts, dst_pts)
        self.drawing_state = "CALIBRATED"
        
        # 发送校准数据到STM32
        self.send_calibration_data()
        
    def send_calibration_data(self):
        """发送A4纸校准数据到STM32"""
        if self.a4_corners:
            corners_str = ",".join([f"{int(x)},{int(y)}" for x, y in self.a4_corners])
            micu_printf(f"motion_CAL,{corners_str}")
            print(f"Sent calibration: {corners_str}")
            
    def generate_triangle(self, center_x, center_y, scale):
        """生成三角形路径"""
        size = scale * 0.5
        height = size * 0.866
        points = [
            (center_x, center_y - height/2),
            (center_x - size/2, center_y + height/2),
            (center_x + size/2, center_y + height/2),
            (center_x, center_y - height/2)  # 回到起点
        ]
        return self.interpolate_path(points, 10)
        
    def generate_rectangle(self, center_x, center_y, scale):
        """生成矩形路径"""
        w, h = scale * 0.4, scale * 0.3
        points = [
            (center_x - w/2, center_y - h/2),
            (center_x + w/2, center_y - h/2),
            (center_x + w/2, center_y + h/2),
            (center_x - w/2, center_y + h/2),
            (center_x - w/2, center_y - h/2)
        ]
        return self.interpolate_path(points, 8)
        
    def generate_circle(self, center_x, center_y, scale):
        """生成圆形路径"""
        radius = scale * 0.25
        points = []
        for i in range(37):  # 36个点 + 回到起点
            angle = i * 2 * np.pi / 36
            x = center_x + radius * np.cos(angle)
            y = center_y + radius * np.sin(angle)
            points.append((x, y))
        return points
        
    def generate_star(self, center_x, center_y, scale):
        """生成五角星路径"""
        outer_radius = scale * 0.25
        inner_radius = outer_radius * 0.4
        points = []
        for i in range(11):  # 10个点 + 回到起点
            angle = i * np.pi / 5
            radius = outer_radius if i % 2 == 0 else inner_radius
            x = center_x + radius * np.cos(angle - np.pi/2)
            y = center_y + radius * np.sin(angle - np.pi/2)
            points.append((x, y))
        return points
        
    def generate_heart(self, center_x, center_y, scale):
        """生成心形路径"""
        points = []
        size = scale * 0.2
        for t in np.linspace(0, 2*np.pi, 50):
            x = size * (16 * np.sin(t)**3)
            y = size * (13 * np.cos(t) - 5 * np.cos(2*t) - 2 * np.cos(3*t) - np.cos(4*t))
            points.append((center_x + x/16, center_y - y/16))
        points.append(points[0])  # 回到起点
        return points
        
    def interpolate_path(self, points, points_per_segment):
        """在路径点之间插值"""
        interpolated = []
        for i in range(len(points) - 1):
            p1, p2 = points[i], points[i + 1]
            for j in range(points_per_segment):
                t = j / points_per_segment
                x = p1[0] + t * (p2[0] - p1[0])
                y = p1[1] + t * (p2[1] - p1[1])
                interpolated.append((x, y))
        interpolated.append(points[-1])
        return interpolated
        
    def generate_current_pattern(self):
        """生成当前选择的图形路径"""
        if self.current_pattern in self.patterns:
            center_x, center_y = self.pattern_position
            pattern_func = self.patterns[self.current_pattern]
            normalized_path = pattern_func(center_x, center_y, self.pattern_scale)
            return normalized_path
        return []
        
    def send_path_to_stm32(self, path):
        """发送路径数据到STM32"""
        if path and len(path) > 0:
            # 将标准化坐标(0-1)转换为整数坐标(0-1000)
            path_str = ",".join([f"{int(x*1000)},{int(y*1000)}" for x, y in path])
            micu_printf(f"motion_PATH,{len(path)},{path_str}")
            print(f"Sent path: {len(path)} points")
            self.drawing_state = "PATH_LOADED"
            
    def start_drawing(self):
        """开始绘图"""
        if self.drawing_state == "PATH_LOADED":
            micu_printf("motion_CMD,START")
            self.drawing_state = "DRAWING"
            print("Drawing started")
            
    def stop_drawing(self):
        """停止绘图"""
        micu_printf("motion_CMD,STOP")
        self.drawing_state = "IDLE"
        print("Drawing stopped")
        
    def next_pattern(self):
        """切换到下一个图形"""
        patterns = list(self.patterns.keys())
        current_idx = patterns.index(self.current_pattern)
        self.current_pattern = patterns[(current_idx + 1) % len(patterns)]
        print(f"Switched to pattern: {self.current_pattern}")

# --------------------------- A4纸检测器类 ---------------------------
class A4PaperDetector:
    def __init__(self):
        self.lower_white = np.array([0, 0, 0])
        self.upper_white = np.array([250, 20, 250])
        self.min_area = 50
        self.max_area = 100000
        self.aspect_ratio = 1.414
        self.tolerance = 0.2
        
    def detect(self, img):
        """检测A4纸并返回四角坐标"""
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        white_mask = cv2.inRange(hsv, self.lower_white, self.upper_white)
        
        # 形态学操作
        kernel = np.ones((3, 3), np.uint8)
        white_mask = cv2.dilate(white_mask, kernel, iterations=1)
        white_mask = cv2.erode(white_mask, kernel, iterations=1)
        
        contours, _ = cv2.findContours(white_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if not (self.min_area <= area <= self.max_area):
                continue
                
            perimeter = cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, 0.03 * perimeter, True)
            
            if len(approx) == 4:
                rect = cv2.minAreaRect(approx)
                width, height = rect[1]
                aspect_ratio = max(width, height) / min(width, height)
                
                if abs(aspect_ratio - self.aspect_ratio) <= self.tolerance:
                    # 排序角点
                    corners = approx.reshape(4, 2).astype(np.float32)
                    s = corners.sum(axis=1)
                    tl = corners[np.argmin(s)]
                    br = corners[np.argmax(s)]
                    diff = np.diff(corners, axis=1)
                    tr = corners[np.argmin(diff)]
                    bl = corners[np.argmax(diff)]
                    return [tl, tr, br, bl], approx
                    
        return None, None

# --------------------------- 参数配置 ---------------------------
CAM_WIDTH, CAM_HEIGHT = 160, 120
DISPLAY_FPS = 60

# 工作模式
MODE_A4_DETECTION = 0
MODE_PATH_PLANNING = 1
MODE_DRAWING = 2

if __name__ == "__main__":
    gc.disable()
    print("激光画图主控制器启动 (摄像头主控模式)...")
    
    # 初始化组件
    disp = display.Display()
    cam = camera.Camera(CAM_WIDTH, CAM_HEIGHT, image.Format.FMT_BGR888, fps=DISPLAY_FPS)
    controller = LaserDrawingMasterController()
    a4_detector = A4PaperDetector()
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
    else:
        print("串口初始化失败")
        exit()
    uart.set_frame("$$", "##", True)
    
    # 状态变量
    current_mode = MODE_A4_DETECTION
    frame_count = 0
    last_time = time.time()
    
    print(f"当前图形: {controller.current_pattern}")
    print("模式: 0-A4检测, 1-路径规划, 2-绘图执行")
    
    while not app.need_exit():
        frame_count += 1
        
        # 每3秒切换模式
        if frame_count % 180 == 0:
            current_mode = (current_mode + 1) % 3
            if current_mode == MODE_PATH_PLANNING:
                controller.next_pattern()
        
        img = cam.read()
        if img is None:
            continue
            
        img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
        img_display = img_cv.copy()
        
        if current_mode == MODE_A4_DETECTION:
            # A4纸检测模式
            corners, contour = a4_detector.detect(img_cv)
            if corners is not None:
                controller.set_a4_corners(corners)
                cv2.drawContours(img_display, [contour], -1, (0, 255, 0), 2)
                cv2.putText(img_display, "A4 Detected", (10, 40),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
                           
        elif current_mode == MODE_PATH_PLANNING:
            # 路径规划模式
            if controller.drawing_state == "CALIBRATED":
                path = controller.generate_current_pattern()
                if path:
                    # 发送路径到STM32
                    if frame_count % 60 == 0:  # 每秒发送一次
                        controller.send_path_to_stm32(path)
                    
                    # 显示路径
                    pixel_path = controller.normalized_to_pixel(path) if hasattr(controller, 'normalized_to_pixel') else []
                    for i, (x, y) in enumerate(pixel_path[:20]):  # 只显示前20个点
                        x, y = int(x), int(y)
                        if 0 <= x < CAM_WIDTH and 0 <= y < CAM_HEIGHT:
                            color = (0, 255, 255) if i == 0 else (255, 255, 0)
                            cv2.circle(img_display, (x, y), 2, color, -1)
                    
                    cv2.putText(img_display, f"Pattern: {controller.current_pattern}", 
                               (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                               
        elif current_mode == MODE_DRAWING:
            # 绘图执行模式
            if controller.drawing_state == "PATH_LOADED":
                if frame_count % 120 == 0:  # 每2秒发送一次开始命令
                    controller.start_drawing()
            
            cv2.putText(img_display, "Drawing Mode", (10, 40),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        
        # 显示状态信息
        current_time = time.time()
        fps = int(frame_count / (current_time - last_time)) if (current_time - last_time) > 0 else 0
        
        mode_names = ["A4_DETECT", "PATH_PLAN", "DRAWING"]
        status_text = f"FPS:{fps} | Mode:{mode_names[current_mode]} | State:{controller.drawing_state}"
        cv2.putText(img_display, status_text, (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        # 显示图像
        img_show = image.cv2image(img_display, bgr=True, copy=False)
        disp.show(img_show)
